{"AgentFlow": [], "AssistantFlow": [], "AssistantCustom": [], "AssistantOpenAI": [], "AssistantAzure": [], "ChatFlow": [{"id": "998b5e42-0c2f-401f-8167-c2eaafc1beb7", "name": "Тест", "flowData": "{\n  \"nodes\": [],\n  \"edges\": []\n}", "type": "CHATFLOW"}, {"id": "0ac6c6b6-52f9-4bce-9afe-a2d959091c76", "name": "UPDL-AR.js", "flowData": "{\n  \"nodes\": [\n    {\n      \"id\": \"Space_0\",\n      \"position\": {\n        \"x\": 829.5775961088707,\n        \"y\": 161.19380884416523\n      },\n      \"type\": \"customNode\",\n      \"data\": {\n        \"id\": \"Space_0\",\n        \"label\": \"Space\",\n        \"version\": 1,\n        \"name\": \"Space\",\n        \"type\": \"UPDLSpace\",\n        \"baseClasses\": [\n          \"UPDLSpace\",\n          \"UPDLNode\"\n        ],\n        \"tags\": [\n          \"UPDL\"\n        ],\n        \"category\": \"UPDL\",\n        \"description\": \"Root node for a 3D space that contains global space settings\",\n        \"inputParams\": [\n          {\n            \"name\": \"spaceName\",\n            \"type\": \"string\",\n            \"label\": \"Space Name\",\n            \"description\": \"Name of the space\",\n            \"default\": \"My Space\",\n            \"id\": \"Space_0-input-spaceName-string\"\n          },\n          {\n            \"name\": \"backgroundColor\",\n            \"type\": \"string\",\n            \"label\": \"Background Color\",\n            \"description\": \"Background color of the space (hex code)\",\n            \"default\": \"\",\n            \"optional\": true,\n            \"additionalParams\": true,\n            \"id\": \"Space_0-input-backgroundColor-string\"\n          },\n          {\n            \"name\": \"skybox\",\n            \"type\": \"boolean\",\n            \"label\": \"Enable Skybox\",\n            \"description\": \"Whether to use a skybox\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Space_0-input-skybox-boolean\"\n          },\n          {\n            \"name\": \"skyboxTexture\",\n            \"type\": \"string\",\n            \"label\": \"Skybox Texture\",\n            \"description\": \"URL to the skybox texture (optional)\",\n            \"optional\": true,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.skybox\": [\n                true\n              ]\n            },\n            \"id\": \"Space_0-input-skyboxTexture-string\"\n          },\n          {\n            \"name\": \"fog\",\n            \"type\": \"boolean\",\n            \"label\": \"Enable Fog\",\n            \"description\": \"Whether to use fog effect\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Space_0-input-fog-boolean\"\n          },\n          {\n            \"name\": \"fogColor\",\n            \"type\": \"string\",\n            \"label\": \"Fog Color\",\n            \"description\": \"Color of the fog (hex code)\",\n            \"default\": \"\",\n            \"optional\": true,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.fog\": [\n                true\n              ]\n            },\n            \"id\": \"Space_0-input-fogColor-string\"\n          },\n          {\n            \"name\": \"fogDensity\",\n            \"type\": \"number\",\n            \"label\": \"Fog Density\",\n            \"description\": \"Density of the fog (0-1)\",\n            \"default\": 0.1,\n            \"min\": 0,\n            \"max\": 1,\n            \"step\": 0.01,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.fog\": [\n                true\n              ]\n            },\n            \"id\": \"Space_0-input-fogDensity-number\"\n          },\n          {\n            \"name\": \"isRootNode\",\n            \"type\": \"boolean\",\n            \"label\": \"Is Root Node\",\n            \"description\": \"Space must be the root node of a UPDL flow\",\n            \"default\": true,\n            \"hidden\": true,\n            \"id\": \"Space_0-input-isRootNode-boolean\"\n          },\n          {\n            \"name\": \"showPoints\",\n            \"type\": \"boolean\",\n            \"label\": \"Show Points Counter\",\n            \"description\": \"Display points counter in the AR interface\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Space_0-input-showPoints-boolean\"\n          },\n          {\n            \"name\": \"collectLeadName\",\n            \"type\": \"boolean\",\n            \"label\": \"Collect Name\",\n            \"description\": \"Collect participant name for quiz data\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Space_0-input-collectLeadName-boolean\"\n          },\n          {\n            \"name\": \"collectLeadEmail\",\n            \"type\": \"boolean\",\n            \"label\": \"Collect Email\",\n            \"description\": \"Collect participant email for quiz data\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Space_0-input-collectLeadEmail-boolean\"\n          },\n          {\n            \"name\": \"collectLeadPhone\",\n            \"type\": \"boolean\",\n            \"label\": \"Collect Phone\",\n            \"description\": \"Collect participant phone for quiz data\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Space_0-input-collectLeadPhone-boolean\"\n          }\n        ],\n        \"inputAnchors\": [\n          {\n            \"label\": \"Spaces\",\n            \"name\": \"spaces\",\n            \"type\": \"UPDLSpace\",\n            \"description\": \"Connect Space nodes to create space chains\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Space_0-input-spaces-UPDLSpace\"\n          },\n          {\n            \"label\": \"Datas\",\n            \"name\": \"data\",\n            \"type\": \"UPDLData\",\n            \"description\": \"Connect Data nodes for quiz questions, answers, and logic\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Space_0-input-data-UPDLData\"\n          },\n          {\n            \"label\": \"Objects\",\n            \"name\": \"objects\",\n            \"type\": \"UPDLObject\",\n            \"description\": \"Connect Object nodes to add them to the space\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Space_0-input-objects-UPDLObject\"\n          },\n          {\n            \"label\": \"Lights\",\n            \"name\": \"lights\",\n            \"type\": \"UPDLLight\",\n            \"description\": \"Connect Light nodes to add them to the space\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Space_0-input-lights-UPDLLight\"\n          },\n          {\n            \"label\": \"Cameras\",\n            \"name\": \"cameras\",\n            \"type\": \"UPDLCamera\",\n            \"description\": \"Connect Camera nodes to add them to the space\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Space_0-input-cameras-UPDLCamera\"\n          }\n        ],\n        \"inputs\": {\n          \"spaces\": \"\",\n          \"spaceName\": \"Start\",\n          \"backgroundColor\": \"\",\n          \"skybox\": \"\",\n          \"skyboxTexture\": \"\",\n          \"fog\": \"\",\n          \"fogColor\": \"\",\n          \"fogDensity\": 0.1,\n          \"isRootNode\": true,\n          \"showPoints\": \"\",\n          \"collectLeadName\": true,\n          \"collectLeadEmail\": \"\",\n          \"collectLeadPhone\": \"\",\n          \"data\": \"\",\n          \"objects\": \"\",\n          \"lights\": \"\",\n          \"cameras\": \"\"\n        },\n        \"outputAnchors\": [\n          {\n            \"id\": \"Space_0-output-Space-UPDLSpace|UPDLNode\",\n            \"name\": \"Space\",\n            \"label\": \"UPDLSpace\",\n            \"description\": \"Root node for a 3D space that contains global space settings\",\n            \"type\": \"UPDLSpace | UPDLNode\"\n          }\n        ],\n        \"outputs\": {\n          \"Space\": \"\"\n        },\n        \"selected\": false\n      },\n      \"width\": 300,\n      \"height\": 585,\n      \"selected\": false,\n      \"dragging\": false,\n      \"positionAbsolute\": {\n        \"x\": 829.5775961088707,\n        \"y\": 161.19380884416523\n      }\n    },\n    {\n      \"id\": \"Space_1\",\n      \"position\": {\n        \"x\": 831.2753951435398,\n        \"y\": 916.1638343673011\n      },\n      \"type\": \"customNode\",\n      \"data\": {\n        \"id\": \"Space_1\",\n        \"label\": \"Space\",\n        \"version\": 1,\n        \"name\": \"Space\",\n        \"type\": \"UPDLSpace\",\n        \"baseClasses\": [\n          \"UPDLSpace\",\n          \"UPDLNode\"\n        ],\n        \"tags\": [\n          \"UPDL\"\n        ],\n        \"category\": \"UPDL\",\n        \"description\": \"Root node for a 3D space that contains global space settings\",\n        \"inputParams\": [\n          {\n            \"name\": \"spaceName\",\n            \"type\": \"string\",\n            \"label\": \"Space Name\",\n            \"description\": \"Name of the space\",\n            \"default\": \"My Space\",\n            \"id\": \"Space_1-input-spaceName-string\"\n          },\n          {\n            \"name\": \"backgroundColor\",\n            \"type\": \"string\",\n            \"label\": \"Background Color\",\n            \"description\": \"Background color of the space (hex code)\",\n            \"default\": \"\",\n            \"optional\": true,\n            \"additionalParams\": true,\n            \"id\": \"Space_1-input-backgroundColor-string\"\n          },\n          {\n            \"name\": \"skybox\",\n            \"type\": \"boolean\",\n            \"label\": \"Enable Skybox\",\n            \"description\": \"Whether to use a skybox\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Space_1-input-skybox-boolean\"\n          },\n          {\n            \"name\": \"skyboxTexture\",\n            \"type\": \"string\",\n            \"label\": \"Skybox Texture\",\n            \"description\": \"URL to the skybox texture (optional)\",\n            \"optional\": true,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.skybox\": [\n                true\n              ]\n            },\n            \"id\": \"Space_1-input-skyboxTexture-string\"\n          },\n          {\n            \"name\": \"fog\",\n            \"type\": \"boolean\",\n            \"label\": \"Enable Fog\",\n            \"description\": \"Whether to use fog effect\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Space_1-input-fog-boolean\"\n          },\n          {\n            \"name\": \"fogColor\",\n            \"type\": \"string\",\n            \"label\": \"Fog Color\",\n            \"description\": \"Color of the fog (hex code)\",\n            \"default\": \"\",\n            \"optional\": true,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.fog\": [\n                true\n              ]\n            },\n            \"id\": \"Space_1-input-fogColor-string\"\n          },\n          {\n            \"name\": \"fogDensity\",\n            \"type\": \"number\",\n            \"label\": \"Fog Density\",\n            \"description\": \"Density of the fog (0-1)\",\n            \"default\": 0.1,\n            \"min\": 0,\n            \"max\": 1,\n            \"step\": 0.01,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.fog\": [\n                true\n              ]\n            },\n            \"id\": \"Space_1-input-fogDensity-number\"\n          },\n          {\n            \"name\": \"isRootNode\",\n            \"type\": \"boolean\",\n            \"label\": \"Is Root Node\",\n            \"description\": \"Space must be the root node of a UPDL flow\",\n            \"default\": true,\n            \"hidden\": true,\n            \"id\": \"Space_1-input-isRootNode-boolean\"\n          },\n          {\n            \"name\": \"showPoints\",\n            \"type\": \"boolean\",\n            \"label\": \"Show Points Counter\",\n            \"description\": \"Display points counter in the AR interface\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Space_1-input-showPoints-boolean\"\n          },\n          {\n            \"name\": \"collectLeadName\",\n            \"type\": \"boolean\",\n            \"label\": \"Collect Name\",\n            \"description\": \"Collect participant name for quiz data\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Space_1-input-collectLeadName-boolean\"\n          },\n          {\n            \"name\": \"collectLeadEmail\",\n            \"type\": \"boolean\",\n            \"label\": \"Collect Email\",\n            \"description\": \"Collect participant email for quiz data\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Space_1-input-collectLeadEmail-boolean\"\n          },\n          {\n            \"name\": \"collectLeadPhone\",\n            \"type\": \"boolean\",\n            \"label\": \"Collect Phone\",\n            \"description\": \"Collect participant phone for quiz data\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Space_1-input-collectLeadPhone-boolean\"\n          }\n        ],\n        \"inputAnchors\": [\n          {\n            \"label\": \"Spaces\",\n            \"name\": \"spaces\",\n            \"type\": \"UPDLSpace\",\n            \"description\": \"Connect Space nodes to create space chains\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Space_1-input-spaces-UPDLSpace\"\n          },\n          {\n            \"label\": \"Datas\",\n            \"name\": \"data\",\n            \"type\": \"UPDLData\",\n            \"description\": \"Connect Data nodes for quiz questions, answers, and logic\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Space_1-input-data-UPDLData\"\n          },\n          {\n            \"label\": \"Objects\",\n            \"name\": \"objects\",\n            \"type\": \"UPDLObject\",\n            \"description\": \"Connect Object nodes to add them to the space\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Space_1-input-objects-UPDLObject\"\n          },\n          {\n            \"label\": \"Lights\",\n            \"name\": \"lights\",\n            \"type\": \"UPDLLight\",\n            \"description\": \"Connect Light nodes to add them to the space\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Space_1-input-lights-UPDLLight\"\n          },\n          {\n            \"label\": \"Cameras\",\n            \"name\": \"cameras\",\n            \"type\": \"UPDLCamera\",\n            \"description\": \"Connect Camera nodes to add them to the space\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Space_1-input-cameras-UPDLCamera\"\n          }\n        ],\n        \"inputs\": {\n          \"spaces\": [\n            \"{{Space_0.data.instance}}\"\n          ],\n          \"spaceName\": \"Level 1\",\n          \"backgroundColor\": \"\",\n          \"skybox\": \"\",\n          \"skyboxTexture\": \"\",\n          \"fog\": \"\",\n          \"fogColor\": \"\",\n          \"fogDensity\": 0.1,\n          \"isRootNode\": true,\n          \"showPoints\": true,\n          \"collectLeadName\": false,\n          \"collectLeadEmail\": \"\",\n          \"collectLeadPhone\": \"\",\n          \"data\": [\n            \"{{Data_0.data.instance}}\"\n          ],\n          \"objects\": \"\",\n          \"lights\": \"\",\n          \"cameras\": \"\"\n        },\n        \"outputAnchors\": [\n          {\n            \"id\": \"Space_1-output-Space-UPDLSpace|UPDLNode\",\n            \"name\": \"Space\",\n            \"label\": \"UPDLSpace\",\n            \"description\": \"Root node for a 3D space that contains global space settings\",\n            \"type\": \"UPDLSpace | UPDLNode\"\n          }\n        ],\n        \"outputs\": {\n          \"Space\": \"\"\n        },\n        \"selected\": false\n      },\n      \"width\": 300,\n      \"height\": 585,\n      \"selected\": false,\n      \"dragging\": false,\n      \"positionAbsolute\": {\n        \"x\": 831.2753951435398,\n        \"y\": 916.1638343673011\n      }\n    },\n    {\n      \"id\": \"Data_0\",\n      \"position\": {\n        \"x\": 416.77543525053557,\n        \"y\": 924.669120881188\n      },\n      \"type\": \"customNode\",\n      \"data\": {\n        \"id\": \"Data_0\",\n        \"label\": \"Data\",\n        \"version\": 1,\n        \"name\": \"Data\",\n        \"type\": \"UPDLData\",\n        \"baseClasses\": [\n          \"UPDLData\",\n          \"UPDLNode\"\n        ],\n        \"tags\": [\n          \"UPDL\"\n        ],\n        \"category\": \"UPDL\",\n        \"description\": \"Universal node for quiz data, questions, answers, and transitions\",\n        \"inputParams\": [\n          {\n            \"name\": \"dataName\",\n            \"type\": \"string\",\n            \"label\": \"Data Name\",\n            \"description\": \"Name of the data element\",\n            \"default\": \"My Data\",\n            \"id\": \"Data_0-input-dataName-string\"\n          },\n          {\n            \"name\": \"dataType\",\n            \"type\": \"options\",\n            \"label\": \"Data Type\",\n            \"description\": \"Type of data this node represents\",\n            \"options\": [\n              {\n                \"label\": \"Question\",\n                \"name\": \"question\",\n                \"description\": \"Quiz question\"\n              },\n              {\n                \"label\": \"Answer\",\n                \"name\": \"answer\",\n                \"description\": \"Quiz answer option\"\n              },\n              {\n                \"label\": \"Intro\",\n                \"name\": \"intro\",\n                \"description\": \"Introduction screen\"\n              },\n              {\n                \"label\": \"Transition\",\n                \"name\": \"transition\",\n                \"description\": \"Screen transition\"\n              }\n            ],\n            \"default\": \"question\",\n            \"id\": \"Data_0-input-dataType-options\"\n          },\n          {\n            \"name\": \"content\",\n            \"type\": \"string\",\n            \"label\": \"Content\",\n            \"description\": \"Main content text (question text, answer text, etc.)\",\n            \"multiline\": true,\n            \"rows\": 3,\n            \"default\": \"\",\n            \"id\": \"Data_0-input-content-string\"\n          },\n          {\n            \"name\": \"isCorrect\",\n            \"type\": \"boolean\",\n            \"label\": \"Is Correct Answer\",\n            \"description\": \"Mark this as the correct answer (only for answer type)\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.dataType\": [\n                \"answer\"\n              ]\n            },\n            \"id\": \"Data_0-input-isCorrect-boolean\"\n          },\n          {\n            \"name\": \"nextSpace\",\n            \"type\": \"string\",\n            \"label\": \"Next Space ID\",\n            \"description\": \"ID of the next space to transition to\",\n            \"optional\": true,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.dataType\": [\n                \"transition\",\n                \"answer\"\n              ]\n            },\n            \"id\": \"Data_0-input-nextSpace-string\"\n          },\n          {\n            \"name\": \"userInputType\",\n            \"type\": \"options\",\n            \"label\": \"User Input Type\",\n            \"description\": \"Type of user input expected\",\n            \"options\": [\n              {\n                \"label\": \"Button Click\",\n                \"name\": \"button\",\n                \"description\": \"Simple button interaction\"\n              },\n              {\n                \"label\": \"Text Input\",\n                \"name\": \"text\",\n                \"description\": \"Text input field\"\n              },\n              {\n                \"label\": \"None\",\n                \"name\": \"none\",\n                \"description\": \"No user input\"\n              }\n            ],\n            \"default\": \"button\",\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.dataType\": [\n                \"intro\",\n                \"transition\"\n              ]\n            },\n            \"id\": \"Data_0-input-userInputType-options\"\n          },\n          {\n            \"name\": \"enablePoints\",\n            \"type\": \"boolean\",\n            \"label\": \"Enable Points\",\n            \"description\": \"Enable point calculation for this data element\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Data_0-input-enablePoints-boolean\"\n          },\n          {\n            \"name\": \"pointsValue\",\n            \"type\": \"number\",\n            \"label\": \"Points Value\",\n            \"description\": \"Points to add/subtract when interacting with this element (-100 to +100)\",\n            \"default\": 1,\n            \"min\": -100,\n            \"max\": 100,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.enablePoints\": [\n                true\n              ]\n            },\n            \"id\": \"Data_0-input-pointsValue-number\"\n          }\n        ],\n        \"inputAnchors\": [\n          {\n            \"label\": \"Datas\",\n            \"name\": \"datas\",\n            \"type\": \"UPDLData\",\n            \"description\": \"Connect Data nodes to create data chains\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Data_0-input-datas-UPDLData\"\n          },\n          {\n            \"label\": \"Objects\",\n            \"name\": \"objects\",\n            \"type\": \"UPDLObject\",\n            \"description\": \"Connect Object nodes to associate with this data\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Data_0-input-objects-UPDLObject\"\n          }\n        ],\n        \"inputs\": {\n          \"datas\": [\n            \"{{Data_1.data.instance}}\",\n            \"{{Data_2.data.instance}}\"\n          ],\n          \"dataName\": \"Q1\",\n          \"dataType\": \"question\",\n          \"content\": \"Как дела?\",\n          \"isCorrect\": \"\",\n          \"nextSpace\": \"\",\n          \"userInputType\": \"button\",\n          \"enablePoints\": \"\",\n          \"pointsValue\": 1,\n          \"objects\": \"\"\n        },\n        \"outputAnchors\": [\n          {\n            \"id\": \"Data_0-output-Data-UPDLData|UPDLNode\",\n            \"name\": \"Data\",\n            \"label\": \"UPDLData\",\n            \"description\": \"Universal node for quiz data, questions, answers, and transitions\",\n            \"type\": \"UPDLData | UPDLNode\"\n          }\n        ],\n        \"outputs\": {\n          \"Data\": \"\"\n        },\n        \"selected\": false\n      },\n      \"width\": 300,\n      \"height\": 705,\n      \"selected\": false,\n      \"positionAbsolute\": {\n        \"x\": 416.77543525053557,\n        \"y\": 924.669120881188\n      },\n      \"dragging\": false\n    },\n    {\n      \"id\": \"Data_1\",\n      \"position\": {\n        \"x\": -46.44396893216786,\n        \"y\": 931.6081836157756\n      },\n      \"type\": \"customNode\",\n      \"data\": {\n        \"id\": \"Data_1\",\n        \"label\": \"Data\",\n        \"version\": 1,\n        \"name\": \"Data\",\n        \"type\": \"UPDLData\",\n        \"baseClasses\": [\n          \"UPDLData\",\n          \"UPDLNode\"\n        ],\n        \"tags\": [\n          \"UPDL\"\n        ],\n        \"category\": \"UPDL\",\n        \"description\": \"Universal node for quiz data, questions, answers, and transitions\",\n        \"inputParams\": [\n          {\n            \"name\": \"dataName\",\n            \"type\": \"string\",\n            \"label\": \"Data Name\",\n            \"description\": \"Name of the data element\",\n            \"default\": \"My Data\",\n            \"id\": \"Data_1-input-dataName-string\"\n          },\n          {\n            \"name\": \"dataType\",\n            \"type\": \"options\",\n            \"label\": \"Data Type\",\n            \"description\": \"Type of data this node represents\",\n            \"options\": [\n              {\n                \"label\": \"Question\",\n                \"name\": \"question\",\n                \"description\": \"Quiz question\"\n              },\n              {\n                \"label\": \"Answer\",\n                \"name\": \"answer\",\n                \"description\": \"Quiz answer option\"\n              },\n              {\n                \"label\": \"Intro\",\n                \"name\": \"intro\",\n                \"description\": \"Introduction screen\"\n              },\n              {\n                \"label\": \"Transition\",\n                \"name\": \"transition\",\n                \"description\": \"Screen transition\"\n              }\n            ],\n            \"default\": \"question\",\n            \"id\": \"Data_1-input-dataType-options\"\n          },\n          {\n            \"name\": \"content\",\n            \"type\": \"string\",\n            \"label\": \"Content\",\n            \"description\": \"Main content text (question text, answer text, etc.)\",\n            \"multiline\": true,\n            \"rows\": 3,\n            \"default\": \"\",\n            \"id\": \"Data_1-input-content-string\"\n          },\n          {\n            \"name\": \"isCorrect\",\n            \"type\": \"boolean\",\n            \"label\": \"Is Correct Answer\",\n            \"description\": \"Mark this as the correct answer (only for answer type)\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.dataType\": [\n                \"answer\"\n              ]\n            },\n            \"id\": \"Data_1-input-isCorrect-boolean\"\n          },\n          {\n            \"name\": \"nextSpace\",\n            \"type\": \"string\",\n            \"label\": \"Next Space ID\",\n            \"description\": \"ID of the next space to transition to\",\n            \"optional\": true,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.dataType\": [\n                \"transition\",\n                \"answer\"\n              ]\n            },\n            \"id\": \"Data_1-input-nextSpace-string\"\n          },\n          {\n            \"name\": \"userInputType\",\n            \"type\": \"options\",\n            \"label\": \"User Input Type\",\n            \"description\": \"Type of user input expected\",\n            \"options\": [\n              {\n                \"label\": \"Button Click\",\n                \"name\": \"button\",\n                \"description\": \"Simple button interaction\"\n              },\n              {\n                \"label\": \"Text Input\",\n                \"name\": \"text\",\n                \"description\": \"Text input field\"\n              },\n              {\n                \"label\": \"None\",\n                \"name\": \"none\",\n                \"description\": \"No user input\"\n              }\n            ],\n            \"default\": \"button\",\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.dataType\": [\n                \"intro\",\n                \"transition\"\n              ]\n            },\n            \"id\": \"Data_1-input-userInputType-options\"\n          },\n          {\n            \"name\": \"enablePoints\",\n            \"type\": \"boolean\",\n            \"label\": \"Enable Points\",\n            \"description\": \"Enable point calculation for this data element\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Data_1-input-enablePoints-boolean\"\n          },\n          {\n            \"name\": \"pointsValue\",\n            \"type\": \"number\",\n            \"label\": \"Points Value\",\n            \"description\": \"Points to add/subtract when interacting with this element (-100 to +100)\",\n            \"default\": 1,\n            \"min\": -100,\n            \"max\": 100,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.enablePoints\": [\n                true\n              ]\n            },\n            \"id\": \"Data_1-input-pointsValue-number\"\n          }\n        ],\n        \"inputAnchors\": [\n          {\n            \"label\": \"Datas\",\n            \"name\": \"datas\",\n            \"type\": \"UPDLData\",\n            \"description\": \"Connect Data nodes to create data chains\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Data_1-input-datas-UPDLData\"\n          },\n          {\n            \"label\": \"Objects\",\n            \"name\": \"objects\",\n            \"type\": \"UPDLObject\",\n            \"description\": \"Connect Object nodes to associate with this data\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Data_1-input-objects-UPDLObject\"\n          }\n        ],\n        \"inputs\": {\n          \"datas\": \"\",\n          \"dataName\": \"A1\",\n          \"dataType\": \"answer\",\n          \"content\": \"Норм!\",\n          \"isCorrect\": true,\n          \"nextSpace\": \"\",\n          \"userInputType\": \"button\",\n          \"enablePoints\": true,\n          \"pointsValue\": 1,\n          \"objects\": [\n            \"{{Object_0.data.instance}}\"\n          ]\n        },\n        \"outputAnchors\": [\n          {\n            \"id\": \"Data_1-output-Data-UPDLData|UPDLNode\",\n            \"name\": \"Data\",\n            \"label\": \"UPDLData\",\n            \"description\": \"Universal node for quiz data, questions, answers, and transitions\",\n            \"type\": \"UPDLData | UPDLNode\"\n          }\n        ],\n        \"outputs\": {\n          \"Data\": \"\"\n        },\n        \"selected\": false\n      },\n      \"width\": 300,\n      \"height\": 705,\n      \"selected\": false,\n      \"positionAbsolute\": {\n        \"x\": -46.44396893216786,\n        \"y\": 931.6081836157756\n      },\n      \"dragging\": false\n    },\n    {\n      \"id\": \"Object_0\",\n      \"position\": {\n        \"x\": -42.590517779166746,\n        \"y\": 1703.231959701922\n      },\n      \"type\": \"customNode\",\n      \"data\": {\n        \"id\": \"Object_0\",\n        \"label\": \"Object\",\n        \"version\": 1,\n        \"name\": \"Object\",\n        \"type\": \"UPDLObject\",\n        \"baseClasses\": [\n          \"UPDLObject\",\n          \"UPDLNode\"\n        ],\n        \"tags\": [\n          \"UPDL\"\n        ],\n        \"category\": \"UPDL\",\n        \"description\": \"3D object that can be added to a scene\",\n        \"inputParams\": [\n          {\n            \"name\": \"name\",\n            \"type\": \"string\",\n            \"label\": \"Object Name\",\n            \"description\": \"Name of the object\",\n            \"default\": \"My Object\",\n            \"id\": \"Object_0-input-name-string\"\n          },\n          {\n            \"name\": \"objectType\",\n            \"type\": \"options\",\n            \"label\": \"Object Type\",\n            \"description\": \"Type of 3D object\",\n            \"options\": [\n              {\n                \"name\": \"box\",\n                \"label\": \"Box\"\n              },\n              {\n                \"name\": \"sphere\",\n                \"label\": \"Sphere\"\n              },\n              {\n                \"name\": \"cylinder\",\n                \"label\": \"Cylinder\"\n              },\n              {\n                \"name\": \"plane\",\n                \"label\": \"Plane\"\n              }\n            ],\n            \"default\": \"box\",\n            \"id\": \"Object_0-input-objectType-options\"\n          },\n          {\n            \"name\": \"positionX\",\n            \"type\": \"number\",\n            \"label\": \"Position X\",\n            \"description\": \"X position of the object\",\n            \"default\": 0,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"id\": \"Object_0-input-positionX-number\"\n          },\n          {\n            \"name\": \"positionY\",\n            \"type\": \"number\",\n            \"label\": \"Position Y\",\n            \"description\": \"Y position of the object\",\n            \"default\": 0.5,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"id\": \"Object_0-input-positionY-number\"\n          },\n          {\n            \"name\": \"positionZ\",\n            \"type\": \"number\",\n            \"label\": \"Position Z\",\n            \"description\": \"Z position of the object\",\n            \"default\": 0,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"id\": \"Object_0-input-positionZ-number\"\n          },\n          {\n            \"name\": \"scale\",\n            \"type\": \"number\",\n            \"label\": \"Scale\",\n            \"description\": \"Uniform scale of the object\",\n            \"default\": 1,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"id\": \"Object_0-input-scale-number\"\n          },\n          {\n            \"name\": \"color\",\n            \"type\": \"string\",\n            \"label\": \"Color\",\n            \"description\": \"Color of the object (hex code)\",\n            \"default\": \"#ff0000\",\n            \"additionalParams\": true,\n            \"id\": \"Object_0-input-color-string\"\n          },\n          {\n            \"name\": \"width\",\n            \"type\": \"number\",\n            \"label\": \"Width\",\n            \"description\": \"Width of the box or plane\",\n            \"default\": 1,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.objectType\": [\n                \"box\",\n                \"plane\"\n              ]\n            },\n            \"id\": \"Object_0-input-width-number\"\n          },\n          {\n            \"name\": \"height\",\n            \"type\": \"number\",\n            \"label\": \"Height\",\n            \"description\": \"Height of the box, plane or cylinder\",\n            \"default\": 1,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.objectType\": [\n                \"box\",\n                \"plane\",\n                \"cylinder\"\n              ]\n            },\n            \"id\": \"Object_0-input-height-number\"\n          },\n          {\n            \"name\": \"depth\",\n            \"type\": \"number\",\n            \"label\": \"Depth\",\n            \"description\": \"Depth of the box\",\n            \"default\": 1,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.objectType\": [\n                \"box\"\n              ]\n            },\n            \"id\": \"Object_0-input-depth-number\"\n          },\n          {\n            \"name\": \"radius\",\n            \"type\": \"number\",\n            \"label\": \"Radius\",\n            \"description\": \"Radius of the sphere or cylinder\",\n            \"default\": 1,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.objectType\": [\n                \"sphere\",\n                \"cylinder\"\n              ]\n            },\n            \"id\": \"Object_0-input-radius-number\"\n          }\n        ],\n        \"inputAnchors\": [],\n        \"inputs\": {\n          \"name\": \"A1\",\n          \"objectType\": \"sphere\",\n          \"positionX\": \"\",\n          \"positionY\": 0.5,\n          \"positionZ\": \"\",\n          \"scale\": 1,\n          \"color\": \"#00ff00\",\n          \"width\": 1,\n          \"height\": 1,\n          \"depth\": 1,\n          \"radius\": 1\n        },\n        \"outputAnchors\": [\n          {\n            \"id\": \"Object_0-output-Object-UPDLObject|UPDLNode\",\n            \"name\": \"Object\",\n            \"label\": \"UPDLObject\",\n            \"description\": \"3D object that can be added to a scene\",\n            \"type\": \"UPDLObject | UPDLNode\"\n          }\n        ],\n        \"outputs\": {\n          \"Object\": \"\"\n        },\n        \"selected\": false\n      },\n      \"width\": 300,\n      \"height\": 427,\n      \"selected\": false,\n      \"positionAbsolute\": {\n        \"x\": -42.590517779166746,\n        \"y\": 1703.231959701922\n      },\n      \"dragging\": false\n    },\n    {\n      \"id\": \"Data_2\",\n      \"position\": {\n        \"x\": -447.2118085035824,\n        \"y\": 928.8325585219405\n      },\n      \"type\": \"customNode\",\n      \"data\": {\n        \"id\": \"Data_2\",\n        \"label\": \"Data\",\n        \"version\": 1,\n        \"name\": \"Data\",\n        \"type\": \"UPDLData\",\n        \"baseClasses\": [\n          \"UPDLData\",\n          \"UPDLNode\"\n        ],\n        \"tags\": [\n          \"UPDL\"\n        ],\n        \"category\": \"UPDL\",\n        \"description\": \"Universal node for quiz data, questions, answers, and transitions\",\n        \"inputParams\": [\n          {\n            \"name\": \"dataName\",\n            \"type\": \"string\",\n            \"label\": \"Data Name\",\n            \"description\": \"Name of the data element\",\n            \"default\": \"My Data\",\n            \"id\": \"Data_2-input-dataName-string\"\n          },\n          {\n            \"name\": \"dataType\",\n            \"type\": \"options\",\n            \"label\": \"Data Type\",\n            \"description\": \"Type of data this node represents\",\n            \"options\": [\n              {\n                \"label\": \"Question\",\n                \"name\": \"question\",\n                \"description\": \"Quiz question\"\n              },\n              {\n                \"label\": \"Answer\",\n                \"name\": \"answer\",\n                \"description\": \"Quiz answer option\"\n              },\n              {\n                \"label\": \"Intro\",\n                \"name\": \"intro\",\n                \"description\": \"Introduction screen\"\n              },\n              {\n                \"label\": \"Transition\",\n                \"name\": \"transition\",\n                \"description\": \"Screen transition\"\n              }\n            ],\n            \"default\": \"question\",\n            \"id\": \"Data_2-input-dataType-options\"\n          },\n          {\n            \"name\": \"content\",\n            \"type\": \"string\",\n            \"label\": \"Content\",\n            \"description\": \"Main content text (question text, answer text, etc.)\",\n            \"multiline\": true,\n            \"rows\": 3,\n            \"default\": \"\",\n            \"id\": \"Data_2-input-content-string\"\n          },\n          {\n            \"name\": \"isCorrect\",\n            \"type\": \"boolean\",\n            \"label\": \"Is Correct Answer\",\n            \"description\": \"Mark this as the correct answer (only for answer type)\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.dataType\": [\n                \"answer\"\n              ]\n            },\n            \"id\": \"Data_2-input-isCorrect-boolean\"\n          },\n          {\n            \"name\": \"nextSpace\",\n            \"type\": \"string\",\n            \"label\": \"Next Space ID\",\n            \"description\": \"ID of the next space to transition to\",\n            \"optional\": true,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.dataType\": [\n                \"transition\",\n                \"answer\"\n              ]\n            },\n            \"id\": \"Data_2-input-nextSpace-string\"\n          },\n          {\n            \"name\": \"userInputType\",\n            \"type\": \"options\",\n            \"label\": \"User Input Type\",\n            \"description\": \"Type of user input expected\",\n            \"options\": [\n              {\n                \"label\": \"Button Click\",\n                \"name\": \"button\",\n                \"description\": \"Simple button interaction\"\n              },\n              {\n                \"label\": \"Text Input\",\n                \"name\": \"text\",\n                \"description\": \"Text input field\"\n              },\n              {\n                \"label\": \"None\",\n                \"name\": \"none\",\n                \"description\": \"No user input\"\n              }\n            ],\n            \"default\": \"button\",\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.dataType\": [\n                \"intro\",\n                \"transition\"\n              ]\n            },\n            \"id\": \"Data_2-input-userInputType-options\"\n          },\n          {\n            \"name\": \"enablePoints\",\n            \"type\": \"boolean\",\n            \"label\": \"Enable Points\",\n            \"description\": \"Enable point calculation for this data element\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Data_2-input-enablePoints-boolean\"\n          },\n          {\n            \"name\": \"pointsValue\",\n            \"type\": \"number\",\n            \"label\": \"Points Value\",\n            \"description\": \"Points to add/subtract when interacting with this element (-100 to +100)\",\n            \"default\": 1,\n            \"min\": -100,\n            \"max\": 100,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.enablePoints\": [\n                true\n              ]\n            },\n            \"id\": \"Data_2-input-pointsValue-number\"\n          }\n        ],\n        \"inputAnchors\": [\n          {\n            \"label\": \"Datas\",\n            \"name\": \"datas\",\n            \"type\": \"UPDLData\",\n            \"description\": \"Connect Data nodes to create data chains\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Data_2-input-datas-UPDLData\"\n          },\n          {\n            \"label\": \"Objects\",\n            \"name\": \"objects\",\n            \"type\": \"UPDLObject\",\n            \"description\": \"Connect Object nodes to associate with this data\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Data_2-input-objects-UPDLObject\"\n          }\n        ],\n        \"inputs\": {\n          \"datas\": \"\",\n          \"dataName\": \"A2\",\n          \"dataType\": \"answer\",\n          \"content\": \"Так и сяк\",\n          \"isCorrect\": false,\n          \"nextSpace\": \"\",\n          \"userInputType\": \"button\",\n          \"enablePoints\": false,\n          \"pointsValue\": 1,\n          \"objects\": [\n            \"{{Object_1.data.instance}}\"\n          ]\n        },\n        \"outputAnchors\": [\n          {\n            \"id\": \"Data_2-output-Data-UPDLData|UPDLNode\",\n            \"name\": \"Data\",\n            \"label\": \"UPDLData\",\n            \"description\": \"Universal node for quiz data, questions, answers, and transitions\",\n            \"type\": \"UPDLData | UPDLNode\"\n          }\n        ],\n        \"outputs\": {\n          \"Data\": \"\"\n        },\n        \"selected\": false\n      },\n      \"width\": 300,\n      \"height\": 705,\n      \"selected\": false,\n      \"positionAbsolute\": {\n        \"x\": -447.2118085035824,\n        \"y\": 928.8325585219405\n      },\n      \"dragging\": false\n    },\n    {\n      \"id\": \"Object_1\",\n      \"position\": {\n        \"x\": -447.5217949913343,\n        \"y\": 1699.0685220611695\n      },\n      \"type\": \"customNode\",\n      \"data\": {\n        \"id\": \"Object_1\",\n        \"label\": \"Object\",\n        \"version\": 1,\n        \"name\": \"Object\",\n        \"type\": \"UPDLObject\",\n        \"baseClasses\": [\n          \"UPDLObject\",\n          \"UPDLNode\"\n        ],\n        \"tags\": [\n          \"UPDL\"\n        ],\n        \"category\": \"UPDL\",\n        \"description\": \"3D object that can be added to a scene\",\n        \"inputParams\": [\n          {\n            \"name\": \"name\",\n            \"type\": \"string\",\n            \"label\": \"Object Name\",\n            \"description\": \"Name of the object\",\n            \"default\": \"My Object\",\n            \"id\": \"Object_1-input-name-string\"\n          },\n          {\n            \"name\": \"objectType\",\n            \"type\": \"options\",\n            \"label\": \"Object Type\",\n            \"description\": \"Type of 3D object\",\n            \"options\": [\n              {\n                \"name\": \"box\",\n                \"label\": \"Box\"\n              },\n              {\n                \"name\": \"sphere\",\n                \"label\": \"Sphere\"\n              },\n              {\n                \"name\": \"cylinder\",\n                \"label\": \"Cylinder\"\n              },\n              {\n                \"name\": \"plane\",\n                \"label\": \"Plane\"\n              }\n            ],\n            \"default\": \"box\",\n            \"id\": \"Object_1-input-objectType-options\"\n          },\n          {\n            \"name\": \"positionX\",\n            \"type\": \"number\",\n            \"label\": \"Position X\",\n            \"description\": \"X position of the object\",\n            \"default\": 0,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"id\": \"Object_1-input-positionX-number\"\n          },\n          {\n            \"name\": \"positionY\",\n            \"type\": \"number\",\n            \"label\": \"Position Y\",\n            \"description\": \"Y position of the object\",\n            \"default\": 0.5,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"id\": \"Object_1-input-positionY-number\"\n          },\n          {\n            \"name\": \"positionZ\",\n            \"type\": \"number\",\n            \"label\": \"Position Z\",\n            \"description\": \"Z position of the object\",\n            \"default\": 0,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"id\": \"Object_1-input-positionZ-number\"\n          },\n          {\n            \"name\": \"scale\",\n            \"type\": \"number\",\n            \"label\": \"Scale\",\n            \"description\": \"Uniform scale of the object\",\n            \"default\": 1,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"id\": \"Object_1-input-scale-number\"\n          },\n          {\n            \"name\": \"color\",\n            \"type\": \"string\",\n            \"label\": \"Color\",\n            \"description\": \"Color of the object (hex code)\",\n            \"default\": \"#ff0000\",\n            \"additionalParams\": true,\n            \"id\": \"Object_1-input-color-string\"\n          },\n          {\n            \"name\": \"width\",\n            \"type\": \"number\",\n            \"label\": \"Width\",\n            \"description\": \"Width of the box or plane\",\n            \"default\": 1,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.objectType\": [\n                \"box\",\n                \"plane\"\n              ]\n            },\n            \"id\": \"Object_1-input-width-number\"\n          },\n          {\n            \"name\": \"height\",\n            \"type\": \"number\",\n            \"label\": \"Height\",\n            \"description\": \"Height of the box, plane or cylinder\",\n            \"default\": 1,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.objectType\": [\n                \"box\",\n                \"plane\",\n                \"cylinder\"\n              ]\n            },\n            \"id\": \"Object_1-input-height-number\"\n          },\n          {\n            \"name\": \"depth\",\n            \"type\": \"number\",\n            \"label\": \"Depth\",\n            \"description\": \"Depth of the box\",\n            \"default\": 1,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.objectType\": [\n                \"box\"\n              ]\n            },\n            \"id\": \"Object_1-input-depth-number\"\n          },\n          {\n            \"name\": \"radius\",\n            \"type\": \"number\",\n            \"label\": \"Radius\",\n            \"description\": \"Radius of the sphere or cylinder\",\n            \"default\": 1,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.objectType\": [\n                \"sphere\",\n                \"cylinder\"\n              ]\n            },\n            \"id\": \"Object_1-input-radius-number\"\n          }\n        ],\n        \"inputAnchors\": [],\n        \"inputs\": {\n          \"name\": \"A2\",\n          \"objectType\": \"box\",\n          \"positionX\": \"\",\n          \"positionY\": 0.5,\n          \"positionZ\": \"\",\n          \"scale\": 1,\n          \"color\": \"#0000ff\",\n          \"width\": 1,\n          \"height\": 1,\n          \"depth\": 1,\n          \"radius\": 1\n        },\n        \"outputAnchors\": [\n          {\n            \"id\": \"Object_1-output-Object-UPDLObject|UPDLNode\",\n            \"name\": \"Object\",\n            \"label\": \"UPDLObject\",\n            \"description\": \"3D object that can be added to a scene\",\n            \"type\": \"UPDLObject | UPDLNode\"\n          }\n        ],\n        \"outputs\": {\n          \"Object\": \"\"\n        },\n        \"selected\": false\n      },\n      \"width\": 300,\n      \"height\": 427,\n      \"selected\": false,\n      \"positionAbsolute\": {\n        \"x\": -447.5217949913343,\n        \"y\": 1699.0685220611695\n      },\n      \"dragging\": false\n    },\n    {\n      \"id\": \"Space_2\",\n      \"position\": {\n        \"x\": 851.1308521743415,\n        \"y\": 3684.245013345725\n      },\n      \"type\": \"customNode\",\n      \"data\": {\n        \"id\": \"Space_2\",\n        \"label\": \"Space\",\n        \"version\": 1,\n        \"name\": \"Space\",\n        \"type\": \"UPDLSpace\",\n        \"baseClasses\": [\n          \"UPDLSpace\",\n          \"UPDLNode\"\n        ],\n        \"tags\": [\n          \"UPDL\"\n        ],\n        \"category\": \"UPDL\",\n        \"description\": \"Root node for a 3D space that contains global space settings\",\n        \"inputParams\": [\n          {\n            \"name\": \"spaceName\",\n            \"type\": \"string\",\n            \"label\": \"Space Name\",\n            \"description\": \"Name of the space\",\n            \"default\": \"My Space\",\n            \"id\": \"Space_2-input-spaceName-string\"\n          },\n          {\n            \"name\": \"backgroundColor\",\n            \"type\": \"string\",\n            \"label\": \"Background Color\",\n            \"description\": \"Background color of the space (hex code)\",\n            \"default\": \"\",\n            \"optional\": true,\n            \"additionalParams\": true,\n            \"id\": \"Space_2-input-backgroundColor-string\"\n          },\n          {\n            \"name\": \"skybox\",\n            \"type\": \"boolean\",\n            \"label\": \"Enable Skybox\",\n            \"description\": \"Whether to use a skybox\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Space_2-input-skybox-boolean\"\n          },\n          {\n            \"name\": \"skyboxTexture\",\n            \"type\": \"string\",\n            \"label\": \"Skybox Texture\",\n            \"description\": \"URL to the skybox texture (optional)\",\n            \"optional\": true,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.skybox\": [\n                true\n              ]\n            },\n            \"id\": \"Space_2-input-skyboxTexture-string\"\n          },\n          {\n            \"name\": \"fog\",\n            \"type\": \"boolean\",\n            \"label\": \"Enable Fog\",\n            \"description\": \"Whether to use fog effect\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Space_2-input-fog-boolean\"\n          },\n          {\n            \"name\": \"fogColor\",\n            \"type\": \"string\",\n            \"label\": \"Fog Color\",\n            \"description\": \"Color of the fog (hex code)\",\n            \"default\": \"\",\n            \"optional\": true,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.fog\": [\n                true\n              ]\n            },\n            \"id\": \"Space_2-input-fogColor-string\"\n          },\n          {\n            \"name\": \"fogDensity\",\n            \"type\": \"number\",\n            \"label\": \"Fog Density\",\n            \"description\": \"Density of the fog (0-1)\",\n            \"default\": 0.1,\n            \"min\": 0,\n            \"max\": 1,\n            \"step\": 0.01,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.fog\": [\n                true\n              ]\n            },\n            \"id\": \"Space_2-input-fogDensity-number\"\n          },\n          {\n            \"name\": \"isRootNode\",\n            \"type\": \"boolean\",\n            \"label\": \"Is Root Node\",\n            \"description\": \"Space must be the root node of a UPDL flow\",\n            \"default\": true,\n            \"hidden\": true,\n            \"id\": \"Space_2-input-isRootNode-boolean\"\n          },\n          {\n            \"name\": \"showPoints\",\n            \"type\": \"boolean\",\n            \"label\": \"Show Points Counter\",\n            \"description\": \"Display points counter in the AR interface\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Space_2-input-showPoints-boolean\"\n          },\n          {\n            \"name\": \"collectLeadName\",\n            \"type\": \"boolean\",\n            \"label\": \"Collect Name\",\n            \"description\": \"Collect participant name for quiz data\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Space_2-input-collectLeadName-boolean\"\n          },\n          {\n            \"name\": \"collectLeadEmail\",\n            \"type\": \"boolean\",\n            \"label\": \"Collect Email\",\n            \"description\": \"Collect participant email for quiz data\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Space_2-input-collectLeadEmail-boolean\"\n          },\n          {\n            \"name\": \"collectLeadPhone\",\n            \"type\": \"boolean\",\n            \"label\": \"Collect Phone\",\n            \"description\": \"Collect participant phone for quiz data\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Space_2-input-collectLeadPhone-boolean\"\n          }\n        ],\n        \"inputAnchors\": [\n          {\n            \"label\": \"Spaces\",\n            \"name\": \"spaces\",\n            \"type\": \"UPDLSpace\",\n            \"description\": \"Connect Space nodes to create space chains\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Space_2-input-spaces-UPDLSpace\"\n          },\n          {\n            \"label\": \"Datas\",\n            \"name\": \"data\",\n            \"type\": \"UPDLData\",\n            \"description\": \"Connect Data nodes for quiz questions, answers, and logic\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Space_2-input-data-UPDLData\"\n          },\n          {\n            \"label\": \"Objects\",\n            \"name\": \"objects\",\n            \"type\": \"UPDLObject\",\n            \"description\": \"Connect Object nodes to add them to the space\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Space_2-input-objects-UPDLObject\"\n          },\n          {\n            \"label\": \"Lights\",\n            \"name\": \"lights\",\n            \"type\": \"UPDLLight\",\n            \"description\": \"Connect Light nodes to add them to the space\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Space_2-input-lights-UPDLLight\"\n          },\n          {\n            \"label\": \"Cameras\",\n            \"name\": \"cameras\",\n            \"type\": \"UPDLCamera\",\n            \"description\": \"Connect Camera nodes to add them to the space\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Space_2-input-cameras-UPDLCamera\"\n          }\n        ],\n        \"inputs\": {\n          \"spaces\": [\n            \"{{Space_3.data.instance}}\"\n          ],\n          \"spaceName\": \"Stop\",\n          \"backgroundColor\": \"\",\n          \"skybox\": \"\",\n          \"skyboxTexture\": \"\",\n          \"fog\": \"\",\n          \"fogColor\": \"\",\n          \"fogDensity\": 0.1,\n          \"isRootNode\": true,\n          \"showPoints\": true,\n          \"collectLeadName\": false,\n          \"collectLeadEmail\": \"\",\n          \"collectLeadPhone\": \"\",\n          \"data\": [],\n          \"objects\": \"\",\n          \"lights\": \"\",\n          \"cameras\": \"\"\n        },\n        \"outputAnchors\": [\n          {\n            \"id\": \"Space_2-output-Space-UPDLSpace|UPDLNode\",\n            \"name\": \"Space\",\n            \"label\": \"UPDLSpace\",\n            \"description\": \"Root node for a 3D space that contains global space settings\",\n            \"type\": \"UPDLSpace | UPDLNode\"\n          }\n        ],\n        \"outputs\": {\n          \"Space\": \"\"\n        },\n        \"selected\": false\n      },\n      \"width\": 300,\n      \"height\": 585,\n      \"selected\": false,\n      \"dragging\": false,\n      \"positionAbsolute\": {\n        \"x\": 851.1308521743415,\n        \"y\": 3684.245013345725\n      }\n    },\n    {\n      \"id\": \"Space_3\",\n      \"position\": {\n        \"x\": 840.6350092133591,\n        \"y\": 2295.0910086336653\n      },\n      \"type\": \"customNode\",\n      \"data\": {\n        \"id\": \"Space_3\",\n        \"label\": \"Space\",\n        \"version\": 1,\n        \"name\": \"Space\",\n        \"type\": \"UPDLSpace\",\n        \"baseClasses\": [\n          \"UPDLSpace\",\n          \"UPDLNode\"\n        ],\n        \"tags\": [\n          \"UPDL\"\n        ],\n        \"category\": \"UPDL\",\n        \"description\": \"Root node for a 3D space that contains global space settings\",\n        \"inputParams\": [\n          {\n            \"name\": \"spaceName\",\n            \"type\": \"string\",\n            \"label\": \"Space Name\",\n            \"description\": \"Name of the space\",\n            \"default\": \"My Space\",\n            \"id\": \"Space_3-input-spaceName-string\"\n          },\n          {\n            \"name\": \"backgroundColor\",\n            \"type\": \"string\",\n            \"label\": \"Background Color\",\n            \"description\": \"Background color of the space (hex code)\",\n            \"default\": \"\",\n            \"optional\": true,\n            \"additionalParams\": true,\n            \"id\": \"Space_3-input-backgroundColor-string\"\n          },\n          {\n            \"name\": \"skybox\",\n            \"type\": \"boolean\",\n            \"label\": \"Enable Skybox\",\n            \"description\": \"Whether to use a skybox\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Space_3-input-skybox-boolean\"\n          },\n          {\n            \"name\": \"skyboxTexture\",\n            \"type\": \"string\",\n            \"label\": \"Skybox Texture\",\n            \"description\": \"URL to the skybox texture (optional)\",\n            \"optional\": true,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.skybox\": [\n                true\n              ]\n            },\n            \"id\": \"Space_3-input-skyboxTexture-string\"\n          },\n          {\n            \"name\": \"fog\",\n            \"type\": \"boolean\",\n            \"label\": \"Enable Fog\",\n            \"description\": \"Whether to use fog effect\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Space_3-input-fog-boolean\"\n          },\n          {\n            \"name\": \"fogColor\",\n            \"type\": \"string\",\n            \"label\": \"Fog Color\",\n            \"description\": \"Color of the fog (hex code)\",\n            \"default\": \"\",\n            \"optional\": true,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.fog\": [\n                true\n              ]\n            },\n            \"id\": \"Space_3-input-fogColor-string\"\n          },\n          {\n            \"name\": \"fogDensity\",\n            \"type\": \"number\",\n            \"label\": \"Fog Density\",\n            \"description\": \"Density of the fog (0-1)\",\n            \"default\": 0.1,\n            \"min\": 0,\n            \"max\": 1,\n            \"step\": 0.01,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.fog\": [\n                true\n              ]\n            },\n            \"id\": \"Space_3-input-fogDensity-number\"\n          },\n          {\n            \"name\": \"isRootNode\",\n            \"type\": \"boolean\",\n            \"label\": \"Is Root Node\",\n            \"description\": \"Space must be the root node of a UPDL flow\",\n            \"default\": true,\n            \"hidden\": true,\n            \"id\": \"Space_3-input-isRootNode-boolean\"\n          },\n          {\n            \"name\": \"showPoints\",\n            \"type\": \"boolean\",\n            \"label\": \"Show Points Counter\",\n            \"description\": \"Display points counter in the AR interface\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Space_3-input-showPoints-boolean\"\n          },\n          {\n            \"name\": \"collectLeadName\",\n            \"type\": \"boolean\",\n            \"label\": \"Collect Name\",\n            \"description\": \"Collect participant name for quiz data\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Space_3-input-collectLeadName-boolean\"\n          },\n          {\n            \"name\": \"collectLeadEmail\",\n            \"type\": \"boolean\",\n            \"label\": \"Collect Email\",\n            \"description\": \"Collect participant email for quiz data\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Space_3-input-collectLeadEmail-boolean\"\n          },\n          {\n            \"name\": \"collectLeadPhone\",\n            \"type\": \"boolean\",\n            \"label\": \"Collect Phone\",\n            \"description\": \"Collect participant phone for quiz data\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Space_3-input-collectLeadPhone-boolean\"\n          }\n        ],\n        \"inputAnchors\": [\n          {\n            \"label\": \"Spaces\",\n            \"name\": \"spaces\",\n            \"type\": \"UPDLSpace\",\n            \"description\": \"Connect Space nodes to create space chains\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Space_3-input-spaces-UPDLSpace\"\n          },\n          {\n            \"label\": \"Datas\",\n            \"name\": \"data\",\n            \"type\": \"UPDLData\",\n            \"description\": \"Connect Data nodes for quiz questions, answers, and logic\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Space_3-input-data-UPDLData\"\n          },\n          {\n            \"label\": \"Objects\",\n            \"name\": \"objects\",\n            \"type\": \"UPDLObject\",\n            \"description\": \"Connect Object nodes to add them to the space\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Space_3-input-objects-UPDLObject\"\n          },\n          {\n            \"label\": \"Lights\",\n            \"name\": \"lights\",\n            \"type\": \"UPDLLight\",\n            \"description\": \"Connect Light nodes to add them to the space\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Space_3-input-lights-UPDLLight\"\n          },\n          {\n            \"label\": \"Cameras\",\n            \"name\": \"cameras\",\n            \"type\": \"UPDLCamera\",\n            \"description\": \"Connect Camera nodes to add them to the space\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Space_3-input-cameras-UPDLCamera\"\n          }\n        ],\n        \"inputs\": {\n          \"spaces\": [\n            \"{{Space_1.data.instance}}\"\n          ],\n          \"spaceName\": \"Level 2\",\n          \"backgroundColor\": \"\",\n          \"skybox\": \"\",\n          \"skyboxTexture\": \"\",\n          \"fog\": \"\",\n          \"fogColor\": \"\",\n          \"fogDensity\": 0.1,\n          \"isRootNode\": true,\n          \"showPoints\": true,\n          \"collectLeadName\": false,\n          \"collectLeadEmail\": \"\",\n          \"collectLeadPhone\": \"\",\n          \"data\": [\n            \"{{Data_3.data.instance}}\"\n          ],\n          \"objects\": \"\",\n          \"lights\": \"\",\n          \"cameras\": \"\"\n        },\n        \"outputAnchors\": [\n          {\n            \"id\": \"Space_3-output-Space-UPDLSpace|UPDLNode\",\n            \"name\": \"Space\",\n            \"label\": \"UPDLSpace\",\n            \"description\": \"Root node for a 3D space that contains global space settings\",\n            \"type\": \"UPDLSpace | UPDLNode\"\n          }\n        ],\n        \"outputs\": {\n          \"Space\": \"\"\n        },\n        \"selected\": false\n      },\n      \"width\": 300,\n      \"height\": 585,\n      \"selected\": false,\n      \"dragging\": false,\n      \"positionAbsolute\": {\n        \"x\": 840.6350092133591,\n        \"y\": 2295.0910086336653\n      }\n    },\n    {\n      \"id\": \"Data_3\",\n      \"position\": {\n        \"x\": 409.0655492004359,\n        \"y\": 2301.879958472232\n      },\n      \"type\": \"customNode\",\n      \"data\": {\n        \"id\": \"Data_3\",\n        \"label\": \"Data\",\n        \"version\": 1,\n        \"name\": \"Data\",\n        \"type\": \"UPDLData\",\n        \"baseClasses\": [\n          \"UPDLData\",\n          \"UPDLNode\"\n        ],\n        \"tags\": [\n          \"UPDL\"\n        ],\n        \"category\": \"UPDL\",\n        \"description\": \"Universal node for quiz data, questions, answers, and transitions\",\n        \"inputParams\": [\n          {\n            \"name\": \"dataName\",\n            \"type\": \"string\",\n            \"label\": \"Data Name\",\n            \"description\": \"Name of the data element\",\n            \"default\": \"My Data\",\n            \"id\": \"Data_3-input-dataName-string\"\n          },\n          {\n            \"name\": \"dataType\",\n            \"type\": \"options\",\n            \"label\": \"Data Type\",\n            \"description\": \"Type of data this node represents\",\n            \"options\": [\n              {\n                \"label\": \"Question\",\n                \"name\": \"question\",\n                \"description\": \"Quiz question\"\n              },\n              {\n                \"label\": \"Answer\",\n                \"name\": \"answer\",\n                \"description\": \"Quiz answer option\"\n              },\n              {\n                \"label\": \"Intro\",\n                \"name\": \"intro\",\n                \"description\": \"Introduction screen\"\n              },\n              {\n                \"label\": \"Transition\",\n                \"name\": \"transition\",\n                \"description\": \"Screen transition\"\n              }\n            ],\n            \"default\": \"question\",\n            \"id\": \"Data_3-input-dataType-options\"\n          },\n          {\n            \"name\": \"content\",\n            \"type\": \"string\",\n            \"label\": \"Content\",\n            \"description\": \"Main content text (question text, answer text, etc.)\",\n            \"multiline\": true,\n            \"rows\": 3,\n            \"default\": \"\",\n            \"id\": \"Data_3-input-content-string\"\n          },\n          {\n            \"name\": \"isCorrect\",\n            \"type\": \"boolean\",\n            \"label\": \"Is Correct Answer\",\n            \"description\": \"Mark this as the correct answer (only for answer type)\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.dataType\": [\n                \"answer\"\n              ]\n            },\n            \"id\": \"Data_3-input-isCorrect-boolean\"\n          },\n          {\n            \"name\": \"nextSpace\",\n            \"type\": \"string\",\n            \"label\": \"Next Space ID\",\n            \"description\": \"ID of the next space to transition to\",\n            \"optional\": true,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.dataType\": [\n                \"transition\",\n                \"answer\"\n              ]\n            },\n            \"id\": \"Data_3-input-nextSpace-string\"\n          },\n          {\n            \"name\": \"userInputType\",\n            \"type\": \"options\",\n            \"label\": \"User Input Type\",\n            \"description\": \"Type of user input expected\",\n            \"options\": [\n              {\n                \"label\": \"Button Click\",\n                \"name\": \"button\",\n                \"description\": \"Simple button interaction\"\n              },\n              {\n                \"label\": \"Text Input\",\n                \"name\": \"text\",\n                \"description\": \"Text input field\"\n              },\n              {\n                \"label\": \"None\",\n                \"name\": \"none\",\n                \"description\": \"No user input\"\n              }\n            ],\n            \"default\": \"button\",\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.dataType\": [\n                \"intro\",\n                \"transition\"\n              ]\n            },\n            \"id\": \"Data_3-input-userInputType-options\"\n          },\n          {\n            \"name\": \"enablePoints\",\n            \"type\": \"boolean\",\n            \"label\": \"Enable Points\",\n            \"description\": \"Enable point calculation for this data element\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Data_3-input-enablePoints-boolean\"\n          },\n          {\n            \"name\": \"pointsValue\",\n            \"type\": \"number\",\n            \"label\": \"Points Value\",\n            \"description\": \"Points to add/subtract when interacting with this element (-100 to +100)\",\n            \"default\": 1,\n            \"min\": -100,\n            \"max\": 100,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.enablePoints\": [\n                true\n              ]\n            },\n            \"id\": \"Data_3-input-pointsValue-number\"\n          }\n        ],\n        \"inputAnchors\": [\n          {\n            \"label\": \"Datas\",\n            \"name\": \"datas\",\n            \"type\": \"UPDLData\",\n            \"description\": \"Connect Data nodes to create data chains\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Data_3-input-datas-UPDLData\"\n          },\n          {\n            \"label\": \"Objects\",\n            \"name\": \"objects\",\n            \"type\": \"UPDLObject\",\n            \"description\": \"Connect Object nodes to associate with this data\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Data_3-input-objects-UPDLObject\"\n          }\n        ],\n        \"inputs\": {\n          \"datas\": [\n            \"{{Data_4.data.instance}}\",\n            \"{{Data_5.data.instance}}\"\n          ],\n          \"dataName\": \"Q2\",\n          \"dataType\": \"question\",\n          \"content\": \"Сделаем?\",\n          \"isCorrect\": \"\",\n          \"nextSpace\": \"\",\n          \"userInputType\": \"button\",\n          \"enablePoints\": \"\",\n          \"pointsValue\": 1,\n          \"objects\": \"\"\n        },\n        \"outputAnchors\": [\n          {\n            \"id\": \"Data_3-output-Data-UPDLData|UPDLNode\",\n            \"name\": \"Data\",\n            \"label\": \"UPDLData\",\n            \"description\": \"Universal node for quiz data, questions, answers, and transitions\",\n            \"type\": \"UPDLData | UPDLNode\"\n          }\n        ],\n        \"outputs\": {\n          \"Data\": \"\"\n        },\n        \"selected\": false\n      },\n      \"width\": 300,\n      \"height\": 705,\n      \"selected\": false,\n      \"positionAbsolute\": {\n        \"x\": 409.0655492004359,\n        \"y\": 2301.879958472232\n      },\n      \"dragging\": false\n    },\n    {\n      \"id\": \"Data_4\",\n      \"position\": {\n        \"x\": -43.761968297052576,\n        \"y\": 2303.857744447436\n      },\n      \"type\": \"customNode\",\n      \"data\": {\n        \"id\": \"Data_4\",\n        \"label\": \"Data\",\n        \"version\": 1,\n        \"name\": \"Data\",\n        \"type\": \"UPDLData\",\n        \"baseClasses\": [\n          \"UPDLData\",\n          \"UPDLNode\"\n        ],\n        \"tags\": [\n          \"UPDL\"\n        ],\n        \"category\": \"UPDL\",\n        \"description\": \"Universal node for quiz data, questions, answers, and transitions\",\n        \"inputParams\": [\n          {\n            \"name\": \"dataName\",\n            \"type\": \"string\",\n            \"label\": \"Data Name\",\n            \"description\": \"Name of the data element\",\n            \"default\": \"My Data\",\n            \"id\": \"Data_4-input-dataName-string\"\n          },\n          {\n            \"name\": \"dataType\",\n            \"type\": \"options\",\n            \"label\": \"Data Type\",\n            \"description\": \"Type of data this node represents\",\n            \"options\": [\n              {\n                \"label\": \"Question\",\n                \"name\": \"question\",\n                \"description\": \"Quiz question\"\n              },\n              {\n                \"label\": \"Answer\",\n                \"name\": \"answer\",\n                \"description\": \"Quiz answer option\"\n              },\n              {\n                \"label\": \"Intro\",\n                \"name\": \"intro\",\n                \"description\": \"Introduction screen\"\n              },\n              {\n                \"label\": \"Transition\",\n                \"name\": \"transition\",\n                \"description\": \"Screen transition\"\n              }\n            ],\n            \"default\": \"question\",\n            \"id\": \"Data_4-input-dataType-options\"\n          },\n          {\n            \"name\": \"content\",\n            \"type\": \"string\",\n            \"label\": \"Content\",\n            \"description\": \"Main content text (question text, answer text, etc.)\",\n            \"multiline\": true,\n            \"rows\": 3,\n            \"default\": \"\",\n            \"id\": \"Data_4-input-content-string\"\n          },\n          {\n            \"name\": \"isCorrect\",\n            \"type\": \"boolean\",\n            \"label\": \"Is Correct Answer\",\n            \"description\": \"Mark this as the correct answer (only for answer type)\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.dataType\": [\n                \"answer\"\n              ]\n            },\n            \"id\": \"Data_4-input-isCorrect-boolean\"\n          },\n          {\n            \"name\": \"nextSpace\",\n            \"type\": \"string\",\n            \"label\": \"Next Space ID\",\n            \"description\": \"ID of the next space to transition to\",\n            \"optional\": true,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.dataType\": [\n                \"transition\",\n                \"answer\"\n              ]\n            },\n            \"id\": \"Data_4-input-nextSpace-string\"\n          },\n          {\n            \"name\": \"userInputType\",\n            \"type\": \"options\",\n            \"label\": \"User Input Type\",\n            \"description\": \"Type of user input expected\",\n            \"options\": [\n              {\n                \"label\": \"Button Click\",\n                \"name\": \"button\",\n                \"description\": \"Simple button interaction\"\n              },\n              {\n                \"label\": \"Text Input\",\n                \"name\": \"text\",\n                \"description\": \"Text input field\"\n              },\n              {\n                \"label\": \"None\",\n                \"name\": \"none\",\n                \"description\": \"No user input\"\n              }\n            ],\n            \"default\": \"button\",\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.dataType\": [\n                \"intro\",\n                \"transition\"\n              ]\n            },\n            \"id\": \"Data_4-input-userInputType-options\"\n          },\n          {\n            \"name\": \"enablePoints\",\n            \"type\": \"boolean\",\n            \"label\": \"Enable Points\",\n            \"description\": \"Enable point calculation for this data element\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Data_4-input-enablePoints-boolean\"\n          },\n          {\n            \"name\": \"pointsValue\",\n            \"type\": \"number\",\n            \"label\": \"Points Value\",\n            \"description\": \"Points to add/subtract when interacting with this element (-100 to +100)\",\n            \"default\": 1,\n            \"min\": -100,\n            \"max\": 100,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.enablePoints\": [\n                true\n              ]\n            },\n            \"id\": \"Data_4-input-pointsValue-number\"\n          }\n        ],\n        \"inputAnchors\": [\n          {\n            \"label\": \"Datas\",\n            \"name\": \"datas\",\n            \"type\": \"UPDLData\",\n            \"description\": \"Connect Data nodes to create data chains\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Data_4-input-datas-UPDLData\"\n          },\n          {\n            \"label\": \"Objects\",\n            \"name\": \"objects\",\n            \"type\": \"UPDLObject\",\n            \"description\": \"Connect Object nodes to associate with this data\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Data_4-input-objects-UPDLObject\"\n          }\n        ],\n        \"inputs\": {\n          \"datas\": \"\",\n          \"dataName\": \"A1\",\n          \"dataType\": \"answer\",\n          \"content\": \"Неа!\",\n          \"isCorrect\": false,\n          \"nextSpace\": \"\",\n          \"userInputType\": \"button\",\n          \"enablePoints\": true,\n          \"pointsValue\": 1,\n          \"objects\": [\n            \"{{Object_2.data.instance}}\"\n          ]\n        },\n        \"outputAnchors\": [\n          {\n            \"id\": \"Data_4-output-Data-UPDLData|UPDLNode\",\n            \"name\": \"Data\",\n            \"label\": \"UPDLData\",\n            \"description\": \"Universal node for quiz data, questions, answers, and transitions\",\n            \"type\": \"UPDLData | UPDLNode\"\n          }\n        ],\n        \"outputs\": {\n          \"Data\": \"\"\n        },\n        \"selected\": false\n      },\n      \"width\": 300,\n      \"height\": 705,\n      \"selected\": false,\n      \"positionAbsolute\": {\n        \"x\": -43.761968297052576,\n        \"y\": 2303.857744447436\n      },\n      \"dragging\": false\n    },\n    {\n      \"id\": \"Data_5\",\n      \"position\": {\n        \"x\": -444.52980786846706,\n        \"y\": 2301.082119353601\n      },\n      \"type\": \"customNode\",\n      \"data\": {\n        \"id\": \"Data_5\",\n        \"label\": \"Data\",\n        \"version\": 1,\n        \"name\": \"Data\",\n        \"type\": \"UPDLData\",\n        \"baseClasses\": [\n          \"UPDLData\",\n          \"UPDLNode\"\n        ],\n        \"tags\": [\n          \"UPDL\"\n        ],\n        \"category\": \"UPDL\",\n        \"description\": \"Universal node for quiz data, questions, answers, and transitions\",\n        \"inputParams\": [\n          {\n            \"name\": \"dataName\",\n            \"type\": \"string\",\n            \"label\": \"Data Name\",\n            \"description\": \"Name of the data element\",\n            \"default\": \"My Data\",\n            \"id\": \"Data_5-input-dataName-string\"\n          },\n          {\n            \"name\": \"dataType\",\n            \"type\": \"options\",\n            \"label\": \"Data Type\",\n            \"description\": \"Type of data this node represents\",\n            \"options\": [\n              {\n                \"label\": \"Question\",\n                \"name\": \"question\",\n                \"description\": \"Quiz question\"\n              },\n              {\n                \"label\": \"Answer\",\n                \"name\": \"answer\",\n                \"description\": \"Quiz answer option\"\n              },\n              {\n                \"label\": \"Intro\",\n                \"name\": \"intro\",\n                \"description\": \"Introduction screen\"\n              },\n              {\n                \"label\": \"Transition\",\n                \"name\": \"transition\",\n                \"description\": \"Screen transition\"\n              }\n            ],\n            \"default\": \"question\",\n            \"id\": \"Data_5-input-dataType-options\"\n          },\n          {\n            \"name\": \"content\",\n            \"type\": \"string\",\n            \"label\": \"Content\",\n            \"description\": \"Main content text (question text, answer text, etc.)\",\n            \"multiline\": true,\n            \"rows\": 3,\n            \"default\": \"\",\n            \"id\": \"Data_5-input-content-string\"\n          },\n          {\n            \"name\": \"isCorrect\",\n            \"type\": \"boolean\",\n            \"label\": \"Is Correct Answer\",\n            \"description\": \"Mark this as the correct answer (only for answer type)\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.dataType\": [\n                \"answer\"\n              ]\n            },\n            \"id\": \"Data_5-input-isCorrect-boolean\"\n          },\n          {\n            \"name\": \"nextSpace\",\n            \"type\": \"string\",\n            \"label\": \"Next Space ID\",\n            \"description\": \"ID of the next space to transition to\",\n            \"optional\": true,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.dataType\": [\n                \"transition\",\n                \"answer\"\n              ]\n            },\n            \"id\": \"Data_5-input-nextSpace-string\"\n          },\n          {\n            \"name\": \"userInputType\",\n            \"type\": \"options\",\n            \"label\": \"User Input Type\",\n            \"description\": \"Type of user input expected\",\n            \"options\": [\n              {\n                \"label\": \"Button Click\",\n                \"name\": \"button\",\n                \"description\": \"Simple button interaction\"\n              },\n              {\n                \"label\": \"Text Input\",\n                \"name\": \"text\",\n                \"description\": \"Text input field\"\n              },\n              {\n                \"label\": \"None\",\n                \"name\": \"none\",\n                \"description\": \"No user input\"\n              }\n            ],\n            \"default\": \"button\",\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.dataType\": [\n                \"intro\",\n                \"transition\"\n              ]\n            },\n            \"id\": \"Data_5-input-userInputType-options\"\n          },\n          {\n            \"name\": \"enablePoints\",\n            \"type\": \"boolean\",\n            \"label\": \"Enable Points\",\n            \"description\": \"Enable point calculation for this data element\",\n            \"default\": false,\n            \"additionalParams\": true,\n            \"id\": \"Data_5-input-enablePoints-boolean\"\n          },\n          {\n            \"name\": \"pointsValue\",\n            \"type\": \"number\",\n            \"label\": \"Points Value\",\n            \"description\": \"Points to add/subtract when interacting with this element (-100 to +100)\",\n            \"default\": 1,\n            \"min\": -100,\n            \"max\": 100,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.enablePoints\": [\n                true\n              ]\n            },\n            \"id\": \"Data_5-input-pointsValue-number\"\n          }\n        ],\n        \"inputAnchors\": [\n          {\n            \"label\": \"Datas\",\n            \"name\": \"datas\",\n            \"type\": \"UPDLData\",\n            \"description\": \"Connect Data nodes to create data chains\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Data_5-input-datas-UPDLData\"\n          },\n          {\n            \"label\": \"Objects\",\n            \"name\": \"objects\",\n            \"type\": \"UPDLObject\",\n            \"description\": \"Connect Object nodes to associate with this data\",\n            \"list\": true,\n            \"optional\": true,\n            \"id\": \"Data_5-input-objects-UPDLObject\"\n          }\n        ],\n        \"inputs\": {\n          \"datas\": \"\",\n          \"dataName\": \"A2\",\n          \"dataType\": \"answer\",\n          \"content\": \"Конеш\",\n          \"isCorrect\": true,\n          \"nextSpace\": \"\",\n          \"userInputType\": \"button\",\n          \"enablePoints\": false,\n          \"pointsValue\": 1,\n          \"objects\": [\n            \"{{Object_3.data.instance}}\"\n          ]\n        },\n        \"outputAnchors\": [\n          {\n            \"id\": \"Data_5-output-Data-UPDLData|UPDLNode\",\n            \"name\": \"Data\",\n            \"label\": \"UPDLData\",\n            \"description\": \"Universal node for quiz data, questions, answers, and transitions\",\n            \"type\": \"UPDLData | UPDLNode\"\n          }\n        ],\n        \"outputs\": {\n          \"Data\": \"\"\n        },\n        \"selected\": false\n      },\n      \"width\": 300,\n      \"height\": 705,\n      \"selected\": false,\n      \"positionAbsolute\": {\n        \"x\": -444.52980786846706,\n        \"y\": 2301.082119353601\n      },\n      \"dragging\": false\n    },\n    {\n      \"id\": \"Object_2\",\n      \"position\": {\n        \"x\": -36.569710426699515,\n        \"y\": 3085.4979406856387\n      },\n      \"type\": \"customNode\",\n      \"data\": {\n        \"id\": \"Object_2\",\n        \"label\": \"Object\",\n        \"version\": 1,\n        \"name\": \"Object\",\n        \"type\": \"UPDLObject\",\n        \"baseClasses\": [\n          \"UPDLObject\",\n          \"UPDLNode\"\n        ],\n        \"tags\": [\n          \"UPDL\"\n        ],\n        \"category\": \"UPDL\",\n        \"description\": \"3D object that can be added to a scene\",\n        \"inputParams\": [\n          {\n            \"name\": \"name\",\n            \"type\": \"string\",\n            \"label\": \"Object Name\",\n            \"description\": \"Name of the object\",\n            \"default\": \"My Object\",\n            \"id\": \"Object_2-input-name-string\"\n          },\n          {\n            \"name\": \"objectType\",\n            \"type\": \"options\",\n            \"label\": \"Object Type\",\n            \"description\": \"Type of 3D object\",\n            \"options\": [\n              {\n                \"name\": \"box\",\n                \"label\": \"Box\"\n              },\n              {\n                \"name\": \"sphere\",\n                \"label\": \"Sphere\"\n              },\n              {\n                \"name\": \"cylinder\",\n                \"label\": \"Cylinder\"\n              },\n              {\n                \"name\": \"plane\",\n                \"label\": \"Plane\"\n              }\n            ],\n            \"default\": \"box\",\n            \"id\": \"Object_2-input-objectType-options\"\n          },\n          {\n            \"name\": \"positionX\",\n            \"type\": \"number\",\n            \"label\": \"Position X\",\n            \"description\": \"X position of the object\",\n            \"default\": 0,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"id\": \"Object_2-input-positionX-number\"\n          },\n          {\n            \"name\": \"positionY\",\n            \"type\": \"number\",\n            \"label\": \"Position Y\",\n            \"description\": \"Y position of the object\",\n            \"default\": 0.5,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"id\": \"Object_2-input-positionY-number\"\n          },\n          {\n            \"name\": \"positionZ\",\n            \"type\": \"number\",\n            \"label\": \"Position Z\",\n            \"description\": \"Z position of the object\",\n            \"default\": 0,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"id\": \"Object_2-input-positionZ-number\"\n          },\n          {\n            \"name\": \"scale\",\n            \"type\": \"number\",\n            \"label\": \"Scale\",\n            \"description\": \"Uniform scale of the object\",\n            \"default\": 1,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"id\": \"Object_2-input-scale-number\"\n          },\n          {\n            \"name\": \"color\",\n            \"type\": \"string\",\n            \"label\": \"Color\",\n            \"description\": \"Color of the object (hex code)\",\n            \"default\": \"#ff0000\",\n            \"additionalParams\": true,\n            \"id\": \"Object_2-input-color-string\"\n          },\n          {\n            \"name\": \"width\",\n            \"type\": \"number\",\n            \"label\": \"Width\",\n            \"description\": \"Width of the box or plane\",\n            \"default\": 1,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.objectType\": [\n                \"box\",\n                \"plane\"\n              ]\n            },\n            \"id\": \"Object_2-input-width-number\"\n          },\n          {\n            \"name\": \"height\",\n            \"type\": \"number\",\n            \"label\": \"Height\",\n            \"description\": \"Height of the box, plane or cylinder\",\n            \"default\": 1,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.objectType\": [\n                \"box\",\n                \"plane\",\n                \"cylinder\"\n              ]\n            },\n            \"id\": \"Object_2-input-height-number\"\n          },\n          {\n            \"name\": \"depth\",\n            \"type\": \"number\",\n            \"label\": \"Depth\",\n            \"description\": \"Depth of the box\",\n            \"default\": 1,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.objectType\": [\n                \"box\"\n              ]\n            },\n            \"id\": \"Object_2-input-depth-number\"\n          },\n          {\n            \"name\": \"radius\",\n            \"type\": \"number\",\n            \"label\": \"Radius\",\n            \"description\": \"Radius of the sphere or cylinder\",\n            \"default\": 1,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.objectType\": [\n                \"sphere\",\n                \"cylinder\"\n              ]\n            },\n            \"id\": \"Object_2-input-radius-number\"\n          }\n        ],\n        \"inputAnchors\": [],\n        \"inputs\": {\n          \"name\": \"A1\",\n          \"objectType\": \"box\",\n          \"positionX\": \"\",\n          \"positionY\": 0.5,\n          \"positionZ\": \"\",\n          \"scale\": 1,\n          \"color\": \"#00ffff\",\n          \"width\": 1,\n          \"height\": 1,\n          \"depth\": 1,\n          \"radius\": 1\n        },\n        \"outputAnchors\": [\n          {\n            \"id\": \"Object_2-output-Object-UPDLObject|UPDLNode\",\n            \"name\": \"Object\",\n            \"label\": \"UPDLObject\",\n            \"description\": \"3D object that can be added to a scene\",\n            \"type\": \"UPDLObject | UPDLNode\"\n          }\n        ],\n        \"outputs\": {\n          \"Object\": \"\"\n        },\n        \"selected\": false\n      },\n      \"width\": 300,\n      \"height\": 427,\n      \"selected\": false,\n      \"positionAbsolute\": {\n        \"x\": -36.569710426699515,\n        \"y\": 3085.4979406856387\n      },\n      \"dragging\": false\n    },\n    {\n      \"id\": \"Object_3\",\n      \"position\": {\n        \"x\": -454.8562145082749,\n        \"y\": 3094.689729914294\n      },\n      \"type\": \"customNode\",\n      \"data\": {\n        \"id\": \"Object_3\",\n        \"label\": \"Object\",\n        \"version\": 1,\n        \"name\": \"Object\",\n        \"type\": \"UPDLObject\",\n        \"baseClasses\": [\n          \"UPDLObject\",\n          \"UPDLNode\"\n        ],\n        \"tags\": [\n          \"UPDL\"\n        ],\n        \"category\": \"UPDL\",\n        \"description\": \"3D object that can be added to a scene\",\n        \"inputParams\": [\n          {\n            \"name\": \"name\",\n            \"type\": \"string\",\n            \"label\": \"Object Name\",\n            \"description\": \"Name of the object\",\n            \"default\": \"My Object\",\n            \"id\": \"Object_3-input-name-string\"\n          },\n          {\n            \"name\": \"objectType\",\n            \"type\": \"options\",\n            \"label\": \"Object Type\",\n            \"description\": \"Type of 3D object\",\n            \"options\": [\n              {\n                \"name\": \"box\",\n                \"label\": \"Box\"\n              },\n              {\n                \"name\": \"sphere\",\n                \"label\": \"Sphere\"\n              },\n              {\n                \"name\": \"cylinder\",\n                \"label\": \"Cylinder\"\n              },\n              {\n                \"name\": \"plane\",\n                \"label\": \"Plane\"\n              }\n            ],\n            \"default\": \"box\",\n            \"id\": \"Object_3-input-objectType-options\"\n          },\n          {\n            \"name\": \"positionX\",\n            \"type\": \"number\",\n            \"label\": \"Position X\",\n            \"description\": \"X position of the object\",\n            \"default\": 0,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"id\": \"Object_3-input-positionX-number\"\n          },\n          {\n            \"name\": \"positionY\",\n            \"type\": \"number\",\n            \"label\": \"Position Y\",\n            \"description\": \"Y position of the object\",\n            \"default\": 0.5,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"id\": \"Object_3-input-positionY-number\"\n          },\n          {\n            \"name\": \"positionZ\",\n            \"type\": \"number\",\n            \"label\": \"Position Z\",\n            \"description\": \"Z position of the object\",\n            \"default\": 0,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"id\": \"Object_3-input-positionZ-number\"\n          },\n          {\n            \"name\": \"scale\",\n            \"type\": \"number\",\n            \"label\": \"Scale\",\n            \"description\": \"Uniform scale of the object\",\n            \"default\": 1,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"id\": \"Object_3-input-scale-number\"\n          },\n          {\n            \"name\": \"color\",\n            \"type\": \"string\",\n            \"label\": \"Color\",\n            \"description\": \"Color of the object (hex code)\",\n            \"default\": \"#ff0000\",\n            \"additionalParams\": true,\n            \"id\": \"Object_3-input-color-string\"\n          },\n          {\n            \"name\": \"width\",\n            \"type\": \"number\",\n            \"label\": \"Width\",\n            \"description\": \"Width of the box or plane\",\n            \"default\": 1,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.objectType\": [\n                \"box\",\n                \"plane\"\n              ]\n            },\n            \"id\": \"Object_3-input-width-number\"\n          },\n          {\n            \"name\": \"height\",\n            \"type\": \"number\",\n            \"label\": \"Height\",\n            \"description\": \"Height of the box, plane or cylinder\",\n            \"default\": 1,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.objectType\": [\n                \"box\",\n                \"plane\",\n                \"cylinder\"\n              ]\n            },\n            \"id\": \"Object_3-input-height-number\"\n          },\n          {\n            \"name\": \"depth\",\n            \"type\": \"number\",\n            \"label\": \"Depth\",\n            \"description\": \"Depth of the box\",\n            \"default\": 1,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.objectType\": [\n                \"box\"\n              ]\n            },\n            \"id\": \"Object_3-input-depth-number\"\n          },\n          {\n            \"name\": \"radius\",\n            \"type\": \"number\",\n            \"label\": \"Radius\",\n            \"description\": \"Radius of the sphere or cylinder\",\n            \"default\": 1,\n            \"step\": 0.1,\n            \"additionalParams\": true,\n            \"show\": {\n              \"inputs.objectType\": [\n                \"sphere\",\n                \"cylinder\"\n              ]\n            },\n            \"id\": \"Object_3-input-radius-number\"\n          }\n        ],\n        \"inputAnchors\": [],\n        \"inputs\": {\n          \"name\": \"A2\",\n          \"objectType\": \"sphere\",\n          \"positionX\": \"\",\n          \"positionY\": 0.5,\n          \"positionZ\": \"\",\n          \"scale\": 1,\n          \"color\": \"#ff00ff\",\n          \"width\": 1,\n          \"height\": 1,\n          \"depth\": 1,\n          \"radius\": 1\n        },\n        \"outputAnchors\": [\n          {\n            \"id\": \"Object_3-output-Object-UPDLObject|UPDLNode\",\n            \"name\": \"Object\",\n            \"label\": \"UPDLObject\",\n            \"description\": \"3D object that can be added to a scene\",\n            \"type\": \"UPDLObject | UPDLNode\"\n          }\n        ],\n        \"outputs\": {\n          \"Object\": \"\"\n        },\n        \"selected\": false\n      },\n      \"width\": 300,\n      \"height\": 427,\n      \"selected\": false,\n      \"positionAbsolute\": {\n        \"x\": -454.8562145082749,\n        \"y\": 3094.689729914294\n      },\n      \"dragging\": false\n    }\n  ],\n  \"edges\": [\n    {\n      \"source\": \"Space_0\",\n      \"sourceHandle\": \"Space_0-output-Space-UPDLSpace|UPDLNode\",\n      \"target\": \"Space_1\",\n      \"targetHandle\": \"Space_1-input-spaces-UPDLSpace\",\n      \"type\": \"buttonedge\",\n      \"id\": \"Space_0-Space_0-output-Space-UPDLSpace|UPDLNode-Space_1-Space_1-input-spaces-UPDLSpace\"\n    },\n    {\n      \"source\": \"Data_0\",\n      \"sourceHandle\": \"Data_0-output-Data-UPDLData|UPDLNode\",\n      \"target\": \"Space_1\",\n      \"targetHandle\": \"Space_1-input-data-UPDLData\",\n      \"type\": \"buttonedge\",\n      \"id\": \"Data_0-Data_0-output-Data-UPDLData|UPDLNode-Space_1-Space_1-input-data-UPDLData\"\n    },\n    {\n      \"source\": \"Data_1\",\n      \"sourceHandle\": \"Data_1-output-Data-UPDLData|UPDLNode\",\n      \"target\": \"Data_0\",\n      \"targetHandle\": \"Data_0-input-datas-UPDLData\",\n      \"type\": \"buttonedge\",\n      \"id\": \"Data_1-Data_1-output-Data-UPDLData|UPDLNode-Data_0-Data_0-input-datas-UPDLData\"\n    },\n    {\n      \"source\": \"Object_0\",\n      \"sourceHandle\": \"Object_0-output-Object-UPDLObject|UPDLNode\",\n      \"target\": \"Data_1\",\n      \"targetHandle\": \"Data_1-input-objects-UPDLObject\",\n      \"type\": \"buttonedge\",\n      \"id\": \"Object_0-Object_0-output-Object-UPDLObject|UPDLNode-Data_1-Data_1-input-objects-UPDLObject\"\n    },\n    {\n      \"source\": \"Data_2\",\n      \"sourceHandle\": \"Data_2-output-Data-UPDLData|UPDLNode\",\n      \"target\": \"Data_0\",\n      \"targetHandle\": \"Data_0-input-datas-UPDLData\",\n      \"type\": \"buttonedge\",\n      \"id\": \"Data_2-Data_2-output-Data-UPDLData|UPDLNode-Data_0-Data_0-input-datas-UPDLData\"\n    },\n    {\n      \"source\": \"Object_1\",\n      \"sourceHandle\": \"Object_1-output-Object-UPDLObject|UPDLNode\",\n      \"target\": \"Data_2\",\n      \"targetHandle\": \"Data_2-input-objects-UPDLObject\",\n      \"type\": \"buttonedge\",\n      \"id\": \"Object_1-Object_1-output-Object-UPDLObject|UPDLNode-Data_2-Data_2-input-objects-UPDLObject\"\n    },\n    {\n      \"source\": \"Space_1\",\n      \"sourceHandle\": \"Space_1-output-Space-UPDLSpace|UPDLNode\",\n      \"target\": \"Space_3\",\n      \"targetHandle\": \"Space_3-input-spaces-UPDLSpace\",\n      \"type\": \"buttonedge\",\n      \"id\": \"Space_1-Space_1-output-Space-UPDLSpace|UPDLNode-Space_3-Space_3-input-spaces-UPDLSpace\"\n    },\n    {\n      \"source\": \"Space_3\",\n      \"sourceHandle\": \"Space_3-output-Space-UPDLSpace|UPDLNode\",\n      \"target\": \"Space_2\",\n      \"targetHandle\": \"Space_2-input-spaces-UPDLSpace\",\n      \"type\": \"buttonedge\",\n      \"id\": \"Space_3-Space_3-output-Space-UPDLSpace|UPDLNode-Space_2-Space_2-input-spaces-UPDLSpace\"\n    },\n    {\n      \"source\": \"Data_3\",\n      \"sourceHandle\": \"Data_3-output-Data-UPDLData|UPDLNode\",\n      \"target\": \"Space_3\",\n      \"targetHandle\": \"Space_3-input-data-UPDLData\",\n      \"type\": \"buttonedge\",\n      \"id\": \"Data_3-Data_3-output-Data-UPDLData|UPDLNode-Space_3-Space_3-input-data-UPDLData\"\n    },\n    {\n      \"source\": \"Data_4\",\n      \"sourceHandle\": \"Data_4-output-Data-UPDLData|UPDLNode\",\n      \"target\": \"Data_3\",\n      \"targetHandle\": \"Data_3-input-datas-UPDLData\",\n      \"type\": \"buttonedge\",\n      \"id\": \"Data_4-Data_4-output-Data-UPDLData|UPDLNode-Data_3-Data_3-input-datas-UPDLData\"\n    },\n    {\n      \"source\": \"Data_5\",\n      \"sourceHandle\": \"Data_5-output-Data-UPDLData|UPDLNode\",\n      \"target\": \"Data_3\",\n      \"targetHandle\": \"Data_3-input-datas-UPDLData\",\n      \"type\": \"buttonedge\",\n      \"id\": \"Data_5-Data_5-output-Data-UPDLData|UPDLNode-Data_3-Data_3-input-datas-UPDLData\"\n    },\n    {\n      \"source\": \"Object_2\",\n      \"sourceHandle\": \"Object_2-output-Object-UPDLObject|UPDLNode\",\n      \"target\": \"Data_4\",\n      \"targetHandle\": \"Data_4-input-objects-UPDLObject\",\n      \"type\": \"buttonedge\",\n      \"id\": \"Object_2-Object_2-output-Object-UPDLObject|UPDLNode-Data_4-Data_4-input-objects-UPDLObject\"\n    },\n    {\n      \"source\": \"Object_3\",\n      \"sourceHandle\": \"Object_3-output-Object-UPDLObject|UPDLNode\",\n      \"target\": \"Data_5\",\n      \"targetHandle\": \"Data_5-input-objects-UPDLObject\",\n      \"type\": \"buttonedge\",\n      \"id\": \"Object_3-Object_3-output-Object-UPDLObject|UPDLNode-Data_5-Data_5-input-objects-UPDLObject\"\n    }\n  ]\n}", "type": "CHATFLOW"}], "ChatMessage": [], "ChatMessageFeedback": [], "CustomTemplate": [], "DocumentStore": [], "DocumentStoreFileChunk": [], "Tool": [], "Variable": []}