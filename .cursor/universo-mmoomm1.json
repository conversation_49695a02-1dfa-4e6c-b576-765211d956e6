{"universo": {"type": "Universo"}, "graph": {"nodes": [{"id": "space_kubio", "type": "Space", "name": "<PERSON><PERSON>"}, {"id": "space_konkordo", "type": "Space", "name": "<PERSON><PERSON><PERSON>"}, {"id": "space_triumfo", "type": "Space", "name": "Triumfo"}, {"id": "ship", "type": "Entity", "name": "Ship"}, {"id": "station_espero", "type": "Entity", "name": "Station Espero"}, {"id": "gate_konkordo", "type": "Entity", "name": "Gate to Konkordo"}, {"id": "gate_triumfo", "type": "Entity", "name": "Gate to Triumfo"}, {"id": "asteroid1", "type": "Entity", "name": "Asteroid 1"}, {"id": "asteroid2", "type": "Entity", "name": "Asteroid 2"}, {"id": "asteroid3", "type": "Entity", "name": "Asteroid 3"}, {"id": "station_omsk8", "type": "Entity", "name": "OMSK-8 Station"}, {"id": "gate_kubio1", "type": "Entity", "name": "Gate to Kubio"}, {"id": "station_krypton", "type": "Entity", "name": "OMSK-Krypton Station"}, {"id": "gate_kubio2", "type": "Entity", "name": "Gate to Kubio"}, {"id": "comp_move", "type": "Component", "componentType": "movement", "target": "ship"}, {"id": "comp_shoot", "type": "Component", "componentType": "shooting", "target": "ship"}, {"id": "comp_trade_espero", "type": "Component", "componentType": "trading", "target": "station_espero"}, {"id": "comp_trade_omsk8", "type": "Component", "componentType": "trading", "target": "station_omsk8"}, {"id": "comp_trade_krypton", "type": "Component", "componentType": "trading", "target": "station_krypton"}, {"id": "event_start", "type": "Event", "eventType": "custom", "name": "OnStart", "source": "ship"}, {"id": "event_shoot", "type": "Event", "eventType": "click", "name": "OnClickShoot", "source": "ship"}, {"id": "event_trade", "type": "Event", "eventType": "click", "name": "OnClickTrade", "source": "station_espero"}, {"id": "event_gate_konkordo", "type": "Event", "eventType": "custom", "name": "OnEnterGate", "source": "gate_konkordo"}, {"id": "event_gate_triumfo", "type": "Event", "eventType": "custom", "name": "OnEnterGate", "source": "gate_triumfo"}, {"id": "event_gate_back1", "type": "Event", "eventType": "custom", "name": "OnEnterGate", "source": "gate_kubio1"}, {"id": "event_gate_back2", "type": "Event", "eventType": "custom", "name": "OnEnterGate", "source": "gate_kubio2"}, {"id": "action_init_data", "type": "Action", "actionType": "SetData", "target": "data_cargo", "params": {"value": 0}}, {"id": "action_init_credits", "type": "Action", "actionType": "SetData", "target": "data_credits", "params": {"value": 0}}, {"id": "action_shoot", "type": "Action", "actionType": "PlaySound", "target": "ship", "params": {"sound": "laser"}}, {"id": "action_move", "type": "Action", "actionType": "Move", "target": "ship"}, {"id": "action_trade", "type": "Action", "actionType": "SetData", "target": "data_credits", "params": {"add": "cargo*10"}}, {"id": "action_gate_konkordo", "type": "Action", "actionType": "SceneSwitch", "target": "space_konkordo"}, {"id": "action_gate_triumfo", "type": "Action", "actionType": "SceneSwitch", "target": "space_triumfo"}, {"id": "action_gate_back_konkordo", "type": "Action", "actionType": "SceneSwitch", "target": "space_kubio"}, {"id": "action_gate_back_triumfo", "type": "Action", "actionType": "SceneSwitch", "target": "space_kubio"}, {"id": "data_cargo", "type": "Data", "key": "cargo", "scope": "Global", "value": 0}, {"id": "data_credits", "type": "Data", "key": "credits", "scope": "Global", "value": 0}], "edges": [{"id": "e1", "source": "space_kubio", "target": "ship"}, {"id": "e2", "source": "space_kubio", "target": "station_espero"}, {"id": "e3", "source": "space_kubio", "target": "gate_konkordo"}, {"id": "e4", "source": "space_kubio", "target": "gate_triumfo"}, {"id": "e5", "source": "space_kubio", "target": "asteroid1"}, {"id": "e6", "source": "space_kubio", "target": "asteroid2"}, {"id": "e7", "source": "space_kubio", "target": "asteroid3"}, {"id": "e8", "source": "ship", "target": "comp_move"}, {"id": "e9", "source": "ship", "target": "comp_shoot"}, {"id": "e10", "source": "station_espero", "target": "comp_trade_espero"}, {"id": "e11", "source": "station_omsk8", "target": "comp_trade_omsk8"}, {"id": "e12", "source": "station_krypton", "target": "comp_trade_krypton"}, {"id": "e13", "source": "event_start", "target": "action_init_data"}, {"id": "e14", "source": "event_start", "target": "action_init_credits"}, {"id": "e15", "source": "event_shoot", "target": "action_shoot"}, {"id": "e16", "source": "event_shoot", "target": "action_move"}, {"id": "e17", "source": "event_trade", "target": "action_trade"}, {"id": "e18", "source": "event_gate_konkordo", "target": "action_gate_konkordo"}, {"id": "e19", "source": "event_gate_triumfo", "target": "action_gate_triumfo"}, {"id": "e20", "source": "event_gate_back1", "target": "action_gate_back_konkordo"}, {"id": "e21", "source": "event_gate_back2", "target": "action_gate_back_triumfo"}]}}