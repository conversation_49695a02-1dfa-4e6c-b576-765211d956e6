---
alwaysApply: false
---
# Multilingual Documentation Guidelines

1.  When updating documentation, such as `README.md`, check if there is a similar file nearby with a version of the documentation in another language, primarily a file with Russian documentation, for example, `README-RU.md`. If such a file exists, it must also be updated.

2.  When updating documentation, the English language file is the primary standard. This file, for example, `README.md`, should be updated first and will always be the standard for versions in other languages. After the English file is updated, versions in other languages, such as the Russian file `README-RU.md`, are updated based on it.

3.  **IMPORTANT!** Files in other languages must fully correspond to the English language file. They must have the same structure, the same content, and the same number of lines. After creating all files in different languages, verify that their content is identical, including the same number of lines.
