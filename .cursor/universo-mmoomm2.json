{"spaces": [{"type": "Space", "id": "kubio", "name": "<PERSON><PERSON>", "entities": [{"type": "Entity", "id": "ship", "name": "Ship"}, {"type": "Entity", "id": "station-espero", "name": "Station Espero"}, {"type": "Entity", "id": "asteroid-1", "name": "Asteroid 1"}, {"type": "Entity", "id": "asteroid-2", "name": "Asteroid 2"}, {"type": "Entity", "id": "gate-konkordo", "name": "Gate to Konkordo"}, {"type": "Entity", "id": "gate-triumfo", "name": "Gate to Triumfo"}], "components": [{"type": "Component", "componentType": "movement", "target": "ship"}, {"type": "Component", "componentType": "shooting", "target": "ship"}, {"type": "Component", "componentType": "trading", "target": "station-espero"}], "events": [{"type": "Event", "id": "start", "eventType": "OnStart"}, {"type": "Event", "id": "shoot", "eventType": "OnClick", "source": "ship"}, {"type": "Event", "id": "trade", "eventType": "OnClick", "source": "station-espero"}, {"type": "Event", "id": "enter-konkordo", "eventType": "OnEnterGate", "source": "gate-konkordo"}, {"type": "Event", "id": "enter-triumfo", "eventType": "OnEnterGate", "source": "gate-triumfo"}], "actions": [{"type": "Action", "actionType": "Move", "target": "ship"}, {"type": "Action", "actionType": "PlaySound", "target": "ship"}, {"type": "Action", "actionType": "SetData", "target": "cargo"}, {"type": "Action", "actionType": "SceneSwitch", "params": {"nextSpace": "konkordo"}}, {"type": "Action", "actionType": "SceneSwitch", "params": {"nextSpace": "triumfo"}}], "datas": [{"type": "Data", "id": "cargo", "key": "cargoAmount", "value": 0}, {"type": "Data", "id": "credits", "key": "credits", "value": 0}]}, {"type": "Space", "id": "konkordo", "name": "<PERSON><PERSON><PERSON>", "entities": [{"type": "Entity", "id": "ship", "name": "Ship"}, {"type": "Entity", "id": "station-omsk8", "name": "OMSK-8"}, {"type": "Entity", "id": "asteroid-a", "name": "Asteroid A"}, {"type": "Entity", "id": "gate-kubio", "name": "Gate to Kubio"}], "components": [{"type": "Component", "componentType": "trading", "target": "station-omsk8"}], "events": [{"type": "Event", "id": "return-kubio", "eventType": "OnEnterGate", "source": "gate-kubio"}], "actions": [{"type": "Action", "actionType": "SceneSwitch", "params": {"nextSpace": "kubio"}}]}, {"type": "Space", "id": "triumfo", "name": "Triumfo", "entities": [{"type": "Entity", "id": "ship", "name": "Ship"}, {"type": "Entity", "id": "station-omsk-krypton", "name": "OMSK-Krypton"}, {"type": "Entity", "id": "asteroid-b", "name": "Asteroid B"}, {"type": "Entity", "id": "gate-kubio2", "name": "Gate to Kubio"}], "components": [{"type": "Component", "componentType": "trading", "target": "station-omsk-krypton"}], "events": [{"type": "Event", "id": "return-kubio2", "eventType": "OnEnterGate", "source": "gate-kubio2"}], "actions": [{"type": "Action", "actionType": "SceneSwitch", "params": {"nextSpace": "kubio"}}]}]}