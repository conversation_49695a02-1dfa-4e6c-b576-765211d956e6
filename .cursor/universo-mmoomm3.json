{"_comment": "Universo Platformo | Example UPDL data for PlayCanvas MMOOMM scene", "spaces": [{"id": "kubio", "name": "<PERSON><PERSON>", "entities": [{"id": "ship", "name": "Ship", "type": "rectangle"}, {"id": "station-espero", "name": "Station Espero", "type": "compound-box"}, {"id": "gate-konkordo", "name": "Gate to Konkordo", "type": "torus"}, {"id": "gate-triumfo", "name": "Gate to Triumfo", "type": "torus"}, {"id": "asteroid-1", "name": "Asteroid 1", "type": "sphere"}, {"id": "asteroid-2", "name": "Asteroid 2", "type": "sphere"}, {"id": "asteroid-3", "name": "Asteroid 3", "type": "sphere"}], "components": [{"id": "move-comp", "componentType": "Movement", "target": "ship"}, {"id": "shoot-comp", "componentType": "Shooting", "target": "ship"}, {"id": "trade-comp-espero", "componentType": "Trading", "target": "station-espero"}], "events": [{"id": "start", "eventType": "OnStart"}, {"id": "shoot-click", "eventType": "OnClick", "source": "ship"}, {"id": "trade-click", "eventType": "OnClick", "source": "station-espero"}, {"id": "enter-konkordo", "eventType": "OnEnterGate", "source": "gate-konkordo"}, {"id": "enter-triumfo", "eventType": "OnEnterGate", "source": "gate-triumfo"}], "actions": [{"id": "move-action", "actionType": "Move", "target": "ship"}, {"id": "shoot-action", "actionType": "PlaySound", "target": "ship", "parameters": {"sound": "shoot"}}, {"id": "trade-action", "actionType": "SetData", "target": "station-espero", "parameters": {"data": "credits"}}, {"id": "switch-konkordo", "actionType": "SceneSwitch", "target": "gate-konkordo", "parameters": {"scene": "<PERSON><PERSON><PERSON>"}}, {"id": "switch-triumfo", "actionType": "SceneSwitch", "target": "gate-triumfo", "parameters": {"scene": "Triumfo"}}], "data": [{"id": "cargo", "key": "cargoAmount", "scope": "Local", "value": 0}, {"id": "credits", "key": "credits", "scope": "Global", "value": 0}]}, {"id": "konkordo", "name": "<PERSON><PERSON><PERSON>", "entities": [{"id": "ship", "name": "Ship", "type": "rectangle"}, {"id": "station-omsk8", "name": "OMSK-8", "type": "torus"}, {"id": "gate-kubio", "name": "Gate to Kubio", "type": "torus"}, {"id": "asteroid-a", "name": "Asteroid A", "type": "sphere"}, {"id": "asteroid-b", "name": "Asteroid B", "type": "sphere"}], "components": [{"id": "move-comp", "componentType": "Movement", "target": "ship"}, {"id": "shoot-comp", "componentType": "Shooting", "target": "ship"}, {"id": "trade-comp-omsk8", "componentType": "Trading", "target": "station-omsk8"}], "events": [{"id": "start", "eventType": "OnStart"}, {"id": "shoot-click", "eventType": "OnClick", "source": "ship"}, {"id": "trade-click", "eventType": "OnClick", "source": "station-omsk8"}, {"id": "enter-kubio", "eventType": "OnEnterGate", "source": "gate-kubio"}], "actions": [{"id": "move-action", "actionType": "Move", "target": "ship"}, {"id": "shoot-action", "actionType": "PlaySound", "target": "ship", "parameters": {"sound": "shoot"}}, {"id": "trade-action", "actionType": "SetData", "target": "station-omsk8", "parameters": {"data": "credits"}}, {"id": "switch-kubio", "actionType": "SceneSwitch", "target": "gate-kubio", "parameters": {"scene": "<PERSON><PERSON>"}}], "data": [{"id": "cargo", "key": "cargoAmount", "scope": "Local", "value": 0}, {"id": "credits", "key": "credits", "scope": "Global", "value": 0}]}, {"id": "triumfo", "name": "Triumfo", "entities": [{"id": "ship", "name": "Ship", "type": "rectangle"}, {"id": "station-omsk-crypton", "name": "OMSK-Krypton", "type": "torus"}, {"id": "gate-kubio", "name": "Gate to Kubio", "type": "torus"}, {"id": "asteroid-x", "name": "Asteroid X", "type": "sphere"}, {"id": "asteroid-y", "name": "Asteroid Y", "type": "sphere"}], "components": [{"id": "move-comp", "componentType": "Movement", "target": "ship"}, {"id": "shoot-comp", "componentType": "Shooting", "target": "ship"}, {"id": "trade-comp-crypton", "componentType": "Trading", "target": "station-omsk-crypton"}], "events": [{"id": "start", "eventType": "OnStart"}, {"id": "shoot-click", "eventType": "OnClick", "source": "ship"}, {"id": "trade-click", "eventType": "OnClick", "source": "station-omsk-crypton"}, {"id": "enter-kubio", "eventType": "OnEnterGate", "source": "gate-kubio"}], "actions": [{"id": "move-action", "actionType": "Move", "target": "ship"}, {"id": "shoot-action", "actionType": "PlaySound", "target": "ship", "parameters": {"sound": "shoot"}}, {"id": "trade-action", "actionType": "SetData", "target": "station-omsk-crypton", "parameters": {"data": "credits"}}, {"id": "switch-kubio", "actionType": "SceneSwitch", "target": "gate-kubio", "parameters": {"scene": "<PERSON><PERSON>"}}], "data": [{"id": "cargo", "key": "cargoAmount", "scope": "Local", "value": 0}, {"id": "credits", "key": "credits", "scope": "Global", "value": 0}]}]}