<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>AR.js - четыре объекта на метке Hiro</title>
    <!-- A-Frame -->
    <script src="https://aframe.io/releases/1.6.0/aframe.min.js"></script>
    <!-- Universo Platformo | AR.js for A-Frame (marker + geolocation, no NFT) -->
    <script src="https://raw.githack.com/AR-js-org/AR.js/master/aframe/build/aframe-ar.js"></script>
    <style>
      body { margin: 0; overflow: hidden; }
    </style>
  </head>

  <body>
    <a-scene embedded arjs>
      <!-- Universo Platformo | Hiro marker -->
      <a-marker preset="hiro">
        <!-- Universo Platformo | Cube 1 -->
        <a-box
          position="-1.5 0.5 0"
          scale="1 1 1"
          material="color: #FF0000">
        </a-box>

        <!-- Universo Platformo | Cube 2 -->
        <a-box
          position="-0.5 0.5 0"
          scale="1 1 1"
          material="color: #0057FF">
        </a-box>

        <!-- Universo Platformo | Sphere -->
        <a-sphere
          position="0.5 0.5 0"
          radius="0.5"
          material="color: #00CC55">
        </a-sphere>

        <!-- Universo Platformo | Cube 3 -->
        <a-box
          position="1.5 0.5 0"
          scale="1 1 1"
          material="color: #FFC800">
        </a-box>
      </a-marker>

      <!-- Universo Platformo | AR.js camera -->
      <a-entity camera></a-entity>
    </a-scene>
  </body>
</html>
