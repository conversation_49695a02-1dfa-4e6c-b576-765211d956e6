<!DOCTYPE html>
<html lang="ru">
<head>
  <meta charset="utf-8" />
  <title>AR.js — кликабельные объекты с нормальной «областью тапа»</title>

  <!-- Universo Platformo | Proper layout on all devices -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no"/>

  <!-- Universo Platformo | A-Frame and AR.js -->
  <script src="https://aframe.io/releases/1.6.0/aframe.min.js"></script>
  <script src="https://raw.githack.com/AR-js-org/AR.js/master/aframe/build/aframe-ar.js"></script>

  <style>
    html,body{margin:0;height:100%;overflow:hidden}
    /* Universo Platformo | Embedded scenes may have padding: remove it */
    a-scene{position:fixed;top:0;left:0;width:100vw;height:100vh}
  </style>

  <!-- Universo Platformo | 1️⃣ Pulse on click -->
  <script>
  AFRAME.registerComponent('grow-on-click',{
    schema:{factor:{default:1.3},dur:{default:150}},
    init(){
      const el=this.el,
            s =el.getAttribute('scale'),
            tgt=`${s.x*this.data.factor} ${s.y*this.data.factor} ${s.z*this.data.factor}`;

      el.addEventListener('click',()=>{
        el.setAttribute('animation__pulse',{
          property:'scale',to:tgt,dir:'alternate',
          dur:this.data.dur,easing:'easeOutQuad',loop:1
        });
      });
    }});
  </script>

  <!-- Universo Platformo | 2️⃣ Grace period (hide/show objects) -->
  <script>
  AFRAME.registerComponent('show-hide-on-marker',{
    schema:{delay:{default:250}},init(){
      const o=this.el.querySelector('#objects'); if(!o) return;
      const d=this.data.delay; let t;
      this.el.addEventListener('markerFound', ()=>{clearTimeout(t);o.setAttribute('visible',true);});
      this.el.addEventListener('markerLost',  ()=>{t=setTimeout(()=>o.setAttribute('visible',false),d);});
    }});
  </script>
</head>

<body>
  <a-scene embedded
           vr-mode-ui="enabled:false"
           renderer="logarithmicDepthBuffer:true; fullscreen:true"
           arjs="sourceType:webcam;
                 sourceWidth:1280; sourceHeight:720;
                 displayWidth:1280; displayHeight:720;
                 trackingMethod:best;
                 debugUIEnabled:false">

    <!-- Universo Platformo | Kanji marker -->
    <a-marker preset="kanji"
              smooth="true" smoothCount="10" minConfidence="0.25"
              show-hide-on-marker>

      <a-entity id="objects" visible="false">
        <a-box    class="clickable" grow-on-click material="color:#FF0000" position="-1.5 0.5 0"></a-box>
        <a-box    class="clickable" grow-on-click material="color:#0057FF" position="-0.5 0.5 0"></a-box>
        <a-sphere class="clickable" grow-on-click material="color:#00CC55" position=" 0.5 0.5 0" radius="0.5"></a-sphere>
        <a-box    class="clickable" grow-on-click material="color:#FFC800" position=" 1.5 0.5 0"></a-box>
      </a-entity>
    </a-marker>

    <!-- Universo Platformo | Camera + visible ray -->
    <a-entity camera look-controls="enabled:false">          <!-- Universo Platformo | AR.js rotates the camera -->
      <a-entity id="mouseRay"
                cursor="rayOrigin:mouse;fuse:false"
                raycaster="objects:.clickable;
                           showLine:true; lineColor:#FF55FF;
                           far:20; interval:0">
      </a-entity>
    </a-entity>
  </a-scene>
</body>
</html>
