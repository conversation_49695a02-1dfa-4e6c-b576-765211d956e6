<!DOCTYPE html>
<html lang="ru">
    <head>
        <meta charset="utf-8" />
        <title>AR.js — устойчивый показ четырёх объектов</title>

        <!-- Universo Platformo | Proper mobile scaling -->
        <meta name="viewport" content="width=device-width,initial-scale=1.0,user-scalable=no" />

        <!-- Universo Platformo | A-Frame and AR.js -->
        <script src="https://aframe.io/releases/1.6.0/aframe.min.js"></script>
        <script src="https://raw.githack.com/AR-js-org/AR.js/master/aframe/build/aframe-ar.js"></script>

        <style>
            body {
                margin: 0;
                overflow: hidden;
            }
        </style>

        <!-- Universo Platformo | Show/hide with delay component -->
        <script>
            AFRAME.registerComponent('show-hide-on-marker', {
                schema: { delay: { type: 'number', default: 250 } },
                init: function () {
                    const objs = this.el.querySelector('#objects')
                    if (!objs) return
                    const delay = this.data.delay
                    let timer

                    this.el.addEventListener('markerFound', function () {
                        clearTimeout(timer)
                        objs.setAttribute('visible', true)
                    })

                    this.el.addEventListener('markerLost', function () {
                        timer = setTimeout(() => objs.setAttribute('visible', false), delay)
                    })
                }
            })
        </script>
    </head>

    <body>
        <a-scene
            embedded
            vr-mode-ui="enabled: false"
            renderer="logarithmicDepthBuffer:true;fullscreen:true"
            arjs="
      sourceType: webcam;
      sourceWidth: 1280; sourceHeight: 720;
      displayWidth: 1280; displayHeight: 720;
      trackingMethod: best;
      debugUIEnabled: false"
        >
            <!-- Universo Platformo | Kanji marker -->
            <a-marker
                preset="kanji"
                smooth="true"
                smoothCount="10"
                smoothTolerance="0.01"
                smoothThreshold="2"
                minConfidence="0.25"
                show-hide-on-marker
            >
                <!-- Universo Platformo | Container with objects -->
                <a-entity id="objects" visible="false">
                    <a-box position="-1.5 0.5 0" material="color:#FF0000"></a-box>
                    <a-box position="-0.5 0.5 0" material="color:#0057FF"></a-box>
                    <a-sphere position="0.5 0.5 0" radius="0.5" material="color:#00CC55"></a-sphere>
                    <a-box position="1.5 0.5 0" material="color:#FFC800"></a-box>
                </a-entity>
            </a-marker>

            <a-entity camera></a-entity>
        </a-scene>
    </body>
</html>
