{"id": "universo-mmoomm", "name": "Universo MMOOMM Example", "version": "0.1.0", "graph": {"nodes": [{"id": "sp-kubio", "type": "Space", "inputs": {"spaceName": "<PERSON><PERSON>"}}, {"id": "sp-konkordo", "type": "Space", "inputs": {"spaceName": "<PERSON><PERSON><PERSON>"}}, {"id": "sp-triumfo", "type": "Space", "inputs": {"spaceName": "Triumfo"}}, {"id": "ent-ship", "type": "Entity", "inputs": {"space": "sp-kubio", "tags": ["ship"]}}, {"id": "ent-espero", "type": "Entity", "inputs": {"space": "sp-kubio", "tags": ["station"]}}, {"id": "ent-omsk8", "type": "Entity", "inputs": {"space": "sp-konkordo", "tags": ["station"]}}, {"id": "ent-krypton", "type": "Entity", "inputs": {"space": "sp-triumfo", "tags": ["station"]}}, {"id": "ent-asteroid-a", "type": "Entity", "inputs": {"space": "sp-kubio", "tags": ["asteroid"]}}, {"id": "ent-asteroid-b", "type": "Entity", "inputs": {"space": "sp-konkordo", "tags": ["asteroid"]}}, {"id": "ent-asteroid-c", "type": "Entity", "inputs": {"space": "sp-triumfo", "tags": ["asteroid"]}}, {"id": "ent-gate-konkordo", "type": "Entity", "inputs": {"space": "sp-kubio", "tags": ["gate"], "targetSpace": "sp-konkordo"}}, {"id": "ent-gate-triumfo", "type": "Entity", "inputs": {"space": "sp-kubio", "tags": ["gate"], "targetSpace": "sp-triumfo"}}, {"id": "ent-gate-kubio-a", "type": "Entity", "inputs": {"space": "sp-konkordo", "tags": ["gate"], "targetSpace": "sp-kubio"}}, {"id": "ent-gate-kubio-b", "type": "Entity", "inputs": {"space": "sp-triumfo", "tags": ["gate"], "targetSpace": "sp-kubio"}}, {"id": "comp-move", "type": "Component", "inputs": {"type": "movement", "target": "ent-ship"}}, {"id": "comp-shoot", "type": "Component", "inputs": {"type": "shooting", "target": "ent-ship"}}, {"id": "comp-trade", "type": "Component", "inputs": {"type": "trading", "target": "ent-ship"}}, {"id": "event-start", "type": "Event", "inputs": {"eventType": "start"}}, {"id": "event-shoot", "type": "Event", "inputs": {"eventType": "click", "source": "ent-ship"}}, {"id": "event-trade", "type": "Event", "inputs": {"eventType": "click", "source": "ent-espero"}}, {"id": "event-enter", "type": "Event", "inputs": {"eventType": "custom", "source": "ent-ship"}}, {"id": "action-move", "type": "Action", "inputs": {"actionType": "move", "target": "ent-ship"}}, {"id": "action-sound", "type": "Action", "inputs": {"actionType": "sound", "target": "ent-ship"}}, {"id": "action-set-cargo", "type": "Action", "inputs": {"actionType": "setData", "target": "data-cargo"}}, {"id": "action-set-credits", "type": "Action", "inputs": {"actionType": "setData", "target": "data-credits"}}, {"id": "action-switch", "type": "Action", "inputs": {"actionType": "sceneSwitch"}}, {"id": "data-cargo", "type": "Data", "inputs": {"dataName": "cargo", "value": "0", "scope": "Global"}}, {"id": "data-credits", "type": "Data", "inputs": {"dataName": "credits", "value": "0", "scope": "Global"}}], "edges": [{"id": "e-start-cargo", "source": "event-start", "target": "action-set-cargo"}, {"id": "e-start-credits", "source": "event-start", "target": "action-set-credits"}, {"id": "e-shoot-sound", "source": "event-shoot", "target": "action-sound"}, {"id": "e-shoot-cargo", "source": "event-shoot", "target": "action-set-cargo"}, {"id": "e-trade-credits", "source": "event-trade", "target": "action-set-credits"}, {"id": "e-enter-switch", "source": "event-enter", "target": "action-switch"}, {"id": "e-move-ship", "source": "comp-move", "target": "ent-ship"}, {"id": "e-shoot-ship", "source": "comp-shoot", "target": "ent-ship"}, {"id": "e-trade-ship", "source": "comp-trade", "target": "ent-ship"}, {"id": "e-cargo-data", "source": "action-set-cargo", "target": "data-cargo"}, {"id": "e-credits-data", "source": "action-set-credits", "target": "data-credits"}]}}