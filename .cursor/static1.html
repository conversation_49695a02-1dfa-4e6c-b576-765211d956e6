<!DOCTYPE html>
<html lang="ru">
    <head>
        <meta charset="utf-8" />
        <title>AR.js — HUD + прозрачный канвас</title>
        <meta name="viewport" content="width=device-width,initial-scale=1.0,user-scalable=no" />

        <!-- Universo Platformo | A-Frame and AR.js -->
        <script src="https://aframe.io/releases/1.6.0/aframe.min.js"></script>
        <script src="https://raw.githack.com/AR-js-org/AR.js/master/aframe/build/aframe-ar.js"></script>

        <style>
            /* Universo Platformo | 0️⃣ Remove opaque background everywhere */
            html,
            body {
                margin: 0;
                height: 100%;
                overflow: hidden;
                background: transparent;
            }
            /* Universo Platformo | 1️⃣ Scene and canvas full screen with transparent background */
            a-scene,
            .a-canvas {
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: transparent !important;
                z-index: 0;
            }
            /* Universo Platformo | 2️⃣ Put AR.js video behind and stretch */
            #arjs-video {
                position: fixed !important;
                top: 0;
                left: 0;
                width: 100vw !important;
                height: 100vh !important;
                object-fit: cover;
                z-index: -2;
                pointer-events: none;
            }
        </style>

        <!-- Universo Platformo | Pulse effect -->
        <script>
            AFRAME.registerComponent('grow-on-click', {
                schema: { factor: { default: 1.3 }, dur: { default: 150 } },
                init() {
                    const el = this.el,
                        s = el.object3D.scale,
                        tgt = `${s.x * this.data.factor} ${s.y * this.data.factor} ${s.z * this.data.factor}`
                    el.addEventListener('click', () =>
                        el.setAttribute('animation__pulse', {
                            property: 'scale',
                            to: tgt,
                            dir: 'alternate',
                            dur: this.data.dur,
                            easing: 'easeOutQuad',
                            loop: 1
                        })
                    )
                }
            })
        </script>
    </head>

    <body>
        <a-scene
            embedded
            vr-mode-ui="enabled:false"
            renderer="alpha:true;antialias:true;fullscreen:true;logarithmicDepthBuffer:true"
            arjs="sourceType:webcam;
                 sourceWidth:1280; sourceHeight:720;
                 displayWidth:1280; displayHeight:720;
                 trackingMethod:best; debugUIEnabled:false"
        >
            <!-- Universo Platformo | Camera -->
            <a-entity camera look-controls="enabled:false">
                <!-- Universo Platformo | Cursor ray -->
                <a-entity cursor="rayOrigin:mouse;fuse:false" raycaster="objects:.clickable;showLine:true;lineColor:#FF55FF;far:20">
                </a-entity>

                <!-- Universo Platformo | HUD 2 meters in front of camera -->
                <a-entity position="0 0 -2">
                    <a-box class="clickable" grow-on-click color="#FF0000" position="-1.1 0 0" scale="0.4 0.4 0.4"></a-box>

                    <a-box class="clickable" grow-on-click color="#0057FF" position="-0.35 0 0" scale="0.4 0.4 0.4"></a-box>

                    <a-sphere class="clickable" grow-on-click color="#00CC55" position="0.35 0 0" radius="0.2"></a-sphere>

                    <a-box class="clickable" grow-on-click color="#FFC800" position="1.1 0 0" scale="0.4 0.4 0.4"></a-box>
                </a-entity>
            </a-entity>
        </a-scene>
    </body>
</html>
