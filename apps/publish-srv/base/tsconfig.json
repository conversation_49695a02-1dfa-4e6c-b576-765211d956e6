{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2021"], "declaration": true, "sourceMap": true, "outDir": "./dist", "strict": true, "strictPropertyInitialization": false, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "skipLibCheck": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "moduleResolution": "node", "composite": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}