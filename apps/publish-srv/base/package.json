{"name": "@universo/publish-srv", "version": "0.1.0", "description": "Backend for publication system in Universo Platformo", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "exports": {".": {"import": "./dist/src/index.js", "require": "./dist/src/index.js", "types": "./dist/src/index.d.ts"}}, "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "pnpm run clean && tsc", "dev": "tsc -w", "lint": "eslint --ext .ts src/"}, "keywords": ["universo", "platformo", "publish", "backend", "3d", "ar", "vr"], "author": "<PERSON> and Tek<PERSON>ko<PERSON>", "license": "Omsk Open License", "dependencies": {"express": "^4.18.2", "typeorm": "^0.3.6", "typescript": "^5.4.5"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.11.17", "eslint": "^8.56.0", "rimraf": "^5.0.5"}}