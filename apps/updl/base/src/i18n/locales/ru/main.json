{"nodeNames": {"scene": "Сцена", "object": "Объект", "camera": "Камера", "light": "Свет", "interaction": "Взаимодействие", "controller": "Контроллер", "animation": "Анимация"}, "nodeDescriptions": {"scene": "Корневой узел для всех сцен UPDL, содержащий глобальные настройки сцены", "object": "3D объект, который можно добавить в сцену", "camera": "Камера, определяющая точку обзора сцены", "light": "Источник света, освещающий сцену", "interaction": "Узел, обрабатывающий события взаимодействия с пользователем", "controller": "Узел, контролирующий поведение других объектов", "animation": "Узел, анимирующий свойства во времени"}, "categories": {"updl": "UPDL", "primitives": "Примитивы", "interaction": "Взаимодействие", "control": "Управление"}, "propertyLabels": {"common": {"name": "Имя", "position": "Позиция", "rotation": "Вращение", "scale": "Масш<PERSON><PERSON><PERSON>", "color": "Цвет", "opacity": "Прозрачность", "visible": "Видимость"}, "scene": {"background": "Цвет фона", "skybox": "Включить Skybox", "skyboxTexture": "Текстура Skybox", "fog": "Включить туман", "fogColor": "Цвет тумана", "fogDensity": "Плотность тумана"}, "object": {"type": "Тип объекта", "modelURL": "URL модели", "primitive": "Примитив", "width": "Ши<PERSON><PERSON><PERSON>", "height": "Высота", "depth": "Гл<PERSON><PERSON><PERSON><PERSON>", "radius": "Радиус"}, "camera": {"mode": "Режим камеры", "fov": "Поле зрения", "near": "Ближняя плоскость отсечения", "far": "Дальняя плоскость отсечения", "lookAt": "Смотреть на", "isActive": "Активная камера"}, "light": {"type": "Тип света", "intensity": "Интенсивность", "direction": "Направление", "angle": "Угол", "penumbra": "Полутень", "range": "Диа<PERSON>азон", "castShadow": "Отбрасывать тень", "shadowMapSize": "Размер карты теней"}}, "options": {"object": {"cube": "<PERSON><PERSON><PERSON>", "sphere": "Сфера", "cylinder": "Цилиндр", "plane": "Плоскость", "model": "3D Модель"}, "camera": {"perspective": "Перспективная", "orthographic": "Ортографическая"}, "light": {"directional": "Направленный", "point": "Точечный", "spot": "Прожектор", "ambient": "Окружающий"}}, "tooltips": {"scene": {"root": "Сцена является корневым контейнером для всех 3D элементов"}, "camera": {"perspective": "Имитирует перспективу человеческого глаза с глубиной", "orthographic": "Без искажения перспективы, хорошо подходит для технических видов"}, "light": {"directional": "Параллельные лучи, имитир<PERSON><PERSON>т удаленный свет, как солнце", "point": "Свет излучается во всех направлениях из одной точки", "spot": "Конусообразный свет с направлением", "ambient": "Глобальное освещение, которое равномерно влияет на все объекты"}}}