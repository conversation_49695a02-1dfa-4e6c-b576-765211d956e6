{"nodeNames": {"scene": "Scene", "object": "Object", "camera": "Camera", "light": "Light", "interaction": "Interaction", "controller": "Controller", "animation": "Animation"}, "nodeDescriptions": {"scene": "Root node for all UPDL scenes that contains global scene settings", "object": "3D object that can be added to a scene", "camera": "Camera that defines the viewpoint for the scene", "light": "Light source that illuminates the scene", "interaction": "Node that handles user interaction events", "controller": "Node that controls behavior of other objects", "animation": "Node that animates properties over time"}, "categories": {"updl": "UPDL", "primitives": "Primitives", "interaction": "Interaction", "control": "Control"}, "propertyLabels": {"common": {"name": "Name", "position": "Position", "rotation": "Rotation", "scale": "Scale", "color": "Color", "opacity": "Opacity", "visible": "Visible"}, "scene": {"background": "Background Color", "skybox": "Enable Skybox", "skyboxTexture": "Skybox Texture", "fog": "Enable Fog", "fogColor": "Fog Color", "fogDensity": "Fog Density"}, "object": {"type": "Object Type", "modelURL": "Model URL", "primitive": "Primitive", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "depth": "De<PERSON><PERSON>", "radius": "<PERSON><PERSON>"}, "camera": {"mode": "Camera Mode", "fov": "Field of View", "near": "Near Clipping Plane", "far": "Far Clipping Plane", "lookAt": "Look At", "isActive": "Active Camera"}, "light": {"type": "Light Type", "intensity": "Intensity", "direction": "Direction", "angle": "<PERSON><PERSON>", "penumbra": "Penumbra", "range": "Range", "castShadow": "Cast Shadow", "shadowMapSize": "Shadow Map Size"}}, "options": {"object": {"cube": "C<PERSON>", "sphere": "Sphere", "cylinder": "<PERSON><PERSON><PERSON>", "plane": "Plane", "model": "3D Model"}, "camera": {"perspective": "Perspective", "orthographic": "Orthographic"}, "light": {"directional": "Directional", "point": "Point", "spot": "Spot", "ambient": "Ambient"}}, "tooltips": {"scene": {"root": "The scene is the root container for all 3D elements"}, "camera": {"perspective": "Mimics human eye perspective with depth", "orthographic": "No perspective distortion, good for technical views"}, "light": {"directional": "Parallel rays, simulates distant light like the sun", "point": "Light radiates in all directions from a single point", "spot": "Cone-shaped light with direction", "ambient": "Global illumination that affects all objects equally"}}}