{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "lib": ["ES2020", "DOM"], "declaration": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "strict": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "skipLibCheck": true, "jsx": "react"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}