{"name": "updl", "version": "0.1.0", "description": "Universo Platformo Definition Language for 3D/AR/VR scene definition - UPDL Node Extensions for Flowise", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "pnpm run clean && tsc && gulp", "dev": "tsc -w", "lint": "eslint --ext .ts,.tsx src/"}, "keywords": ["updl", "universo", "platformo", "flowise", "nodes", "3d", "ar", "vr", "webxr"], "author": "<PERSON> and Tek<PERSON>ko<PERSON>", "license": "Omsk Open License", "dependencies": {"typescript": "^5.4.5"}, "devDependencies": {"@types/gulp": "4.0.9", "@types/node": "^20.11.17", "eslint": "^8.56.0", "gulp": "^4.0.2", "rimraf": "^5.0.5"}}