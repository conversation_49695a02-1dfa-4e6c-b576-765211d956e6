# UPDL (Universal Platform Definition Language)

Система определения узлов для создания универсальных 3D/AR/VR‑пространств в Flowise.

## Описание

UPDL предоставляет набор специализированных узлов для редактора Flowise, позволяя пользователям создавать высокоуровневые абстрактные описания 3D‑пространств. Эти описания затем могут быть экспортированы в различные технологии (например, AR.js, PlayCanvas) через приложения для публикации.

## Архитектура

UPDL — это чистый модуль определения узлов, который легко интегрируется с Flowise:

-   **Только определения узлов**: Содержит только классы узлов для Flowise.
-   **Без логики экспорта**: Вся логика по построению и экспорту пространств находится в системе публикации.
-   **Чистая интеграция**: Загружается в Flowise через механизм `NodesPool` из каталога `dist/nodes`.
-   **Минимальные зависимости**: Содержит только зависимости, необходимые для определения узлов.

## Структура

Исходный код имеет модульную структуру, где каждый высокоуровневый узел находится в своем каталоге.

```
src/
├── assets/              # Статические ресурсы (иконки)
│   └── icons/
├── i18n/                # Ресурсы для интернационализации
├── interfaces/          # Основные интерфейсы TypeScript для экосистемы UPDL
│   └── UPDLInterfaces.ts
├── nodes/               # Определения узлов UPDL
│   ├── action/          # ActionNode: выполняет игровое действие
│   ├── base/            # BaseUPDLNode: общий базовый класс для всех узлов UPDL
│   ├── camera/          # CameraNode: определяет точку обзора
│   ├── component/       # ComponentNode: прикрепляет поведение к Entity
│   ├── data/            # DataNode: хранилище данных "ключ-значение"
│   ├── entity/          # EntityNode: представляет игровой объект в рантайме
│   ├── event/           # EventNode: инициирует действия на основе событий
│   ├── light/           # LightNode: определяет освещение для пространства
│   ├── object/          # ObjectNode (Legacy): определяет простой 3D-объект
│   ├── space/           # SpaceNode: корневой контейнер для сцены
│   ├── universo/        # UniversoNode: глобальные настройки для MMOOMM
│   └── interfaces.ts    # Общие интерфейсы для узлов
└── index.ts             # Главная точка входа - экспортирует все классы и интерфейсы узлов
```

## Интеграция узлов

Модуль UPDL предоставляет определения узлов, которые интегрируются с редактором Flowise.

### Поддерживаемые типы узлов

Система UPDL построена вокруг **7 основных высокоуровневых узлов**, которые обеспечивают полную структуру для описания интерактивных 3D/AR/VR-опытов:

| Узел          | Назначение                                                          | Ключевые поля                          |
| ------------- | ------------------------------------------------------------------- | -------------------------------------- |
| **Space**     | Контейнеры сцен/экранов. Могут быть вложенными                      | id, type (root/module/block), settings |
| **Entity**    | Позиционированный объект/актор внутри Space                         | transform, tags                        |
| **Component** | Добавляет данные/поведение к Entity (рендер, звук, скрипт)          | type, props                            |
| **Event**     | Триггер (OnStart, OnClick, OnTimer...)                              | eventType, source                      |
| **Action**    | Исполнитель (Move, PlaySound, SetData...)                           | actionType, target, params             |
| **Data**      | Хранилище значений; области видимости: Локальная, Space, Глобальная | key, scope, value                      |
| **Universo**  | Шлюз к глобальной сети Kiberplano (GraphQL, MQTT UNS, OPC UA)       | transports, discovery, security        |

#### Поддержка основных узлов UPDL

Система шаблонов в первую очередь предназначена для обработки 7 основных высокоуровневых узлов UPDL:

-   **Space**: Контейнеры сцен/экранов
-   **Entity**: Позиционированные объекты/акторы
-   **Component**: Прикрепления поведения/данных
-   **Event**: Триггеры (OnStart, OnClick и т.д.)
-   **Action**: Исполнители (Move, PlaySound и т.д.)
-   **Data**: Хранилище ключ-значение
-   **Universo**: Глобальная сетевая связность

**Примечание**: Другие узлы (Object, Camera, Light) являются устаревшими/тестовыми узлами и могут быть существенно изменены или удалены в будущих версиях. Сосредоточьте разработку на 7 основных узлах.

### Руководство по реализации коннекторов

Чтобы узлы корректно соединялись на холсте Flowise, следуйте этим правилам:

1.  **Входные коннекторы**: Чтобы родительский узел мог принимать дочерний узел, определите соединение в массиве `inputs` класса родительского узла. Поле `type` в определении входа должно совпадать с `name` дочернего узла (например, `type: 'UPDLEntity'`).

2.  **Выходные коннекторы**: Чтобы получить стандартный выходной коннектор, просто убедитесь, что массив `outputs` в классе узла пуст (`this.outputs = [];`). Flowise сгенерирует его автоматически. **Не** пытайтесь добавлять выход по умолчанию в базовом классе, так как это нарушит механизм.

3.  **Терминальные узлы**: Для узлов вроде `ActionNode`, которые настраиваются внутренне и не соединяются с другими узлами, определите и `inputs`, и `outputs` как пустые массивы.

## Процесс сборки

Процесс сборки состоит из двух этапов:

1.  **Компиляция TypeScript**: Компилирует файлы TypeScript (`.ts`) в JavaScript (`.js`).
2.  **Задачи Gulp**: Копирует все статические ресурсы (например, иконки SVG) из исходных каталогов в папку `dist`, сохраняя структуру каталогов.

### Доступные скрипты

-   `pnpm clean` - Очищает каталог `dist`.
-   `pnpm build` - Собирает пакет (выполняет компиляцию TypeScript и задачи Gulp).
-   `pnpm dev` - Запускает сборку в режиме разработки с отслеживанием изменений файлов.
-   `pnpm lint` - Проверяет код с помощью линтера.

## Фокус модуля

Этот модуль намеренно сфокусирован **только на определениях узлов**:

-   **Без сборщиков пространств**: Обрабатывается системой публикации (`publish-frt`).
-   **Без логики экспорта**: Обрабатывается приложениями для публикации.
-   **Без API-клиентов**: Не требуется для определений узлов.
-   **Без управления состоянием**: Узлы являются определениями без состояния.

Такое четкое разделение обеспечивает оптимальную архитектуру и удобство сопровождения.

---

_Universo Platformo | Модуль UPDL_
