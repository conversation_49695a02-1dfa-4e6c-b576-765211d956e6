# Фронтенд публикации (publish-frt)

Фронтенд для системы публикации в Universo Platformo, с поддержкой AR.js и PlayCanvas.

## Структура проекта

Проект следует единой структуре для приложений в монорепозитории:

```
apps/publish-frt/base/
├─ package.json
├─ tsconfig.json
├─ gulpfile.ts
└─ src/
   ├─ assets/              # Статические файлы (изображения, шрифты, иконки)
   │  ├─ icons/            # SVG-иконки для компонентов и UI
   │  ├─ images/           # Изображения для элементов UI
   │  └─ libs/             # Локальные библиотеки для регионов с блокировкой CDN
   │     ├─ aframe/        # Версии библиотеки A-Frame
   │     └─ arjs/          # Версии библиотеки AR.js
   ├─ api/                 # HTTP-клиенты для взаимодействия с бэкендом
   │  ├─ common.ts         # Базовые утилиты API (аутентификация, парсинг URL, базовый URL)
   │  ├─ index.ts          # Модуль централизованного экспорта API
   │  └─ publication/      # API-клиенты, специфичные для публикации
   │     ├─ PublicationApi.ts        # Базовый API публикации для всех технологий
   │     ├─ ARJSPublicationApi.ts    # Специфичный API для публикации AR.js
   │     ├─ PlayCanvasPublicationApi.ts # Специфичный API для публикации PlayCanvas
   │     ├─ StreamingPublicationApi.ts # API для потоковой публикации
   │     └─ index.ts       # Экспорт API публикации с алиасами для совместимости
   ├─ builders/            # Конвертеры UPDL в целевые платформы с template-first архитектурой
   │  ├─ common/           # Общая инфраструктура билдеров
   │  │  ├─ AbstractTemplateBuilder.ts # Абстрактный базовый класс для всех шаблонов
   │  │  ├─ BaseBuilder.ts           # Базовый класс билдера для высокоуровневых билдеров
   │  │  ├─ BuilderRegistry.ts       # Реестр для управления высокоуровневыми билдерами
   │  │  ├─ TemplateRegistry.ts      # Реестр для управления реализациями шаблонов
   │  │  ├─ UPDLProcessor.ts         # Обработка потоков UPDL
   │  │  ├─ types.ts                 # Общие типы и интерфейсы
   │  │  └─ setup.ts                 # Настройка регистрации билдеров и шаблонов
   │  ├─ templates/        # Организация с приоритетом шаблонов (НОВАЯ АРХИТЕКТУРА)
   │  │  ├─ quiz/          # Шаблон квиза для образовательного контента
   │  │  │  └─ arjs/       # AR.js реализация шаблона квиза
   │  │  │     ├─ ARJSBuilder.ts         # Высокоуровневый AR.js билдер
   │  │  │     ├─ ARJSQuizBuilder.ts     # Реализация шаблона квиза
   │  │  │     ├─ config.ts              # Конфигурация шаблона квиза
   │  │  │     ├─ handlers/              # Процессоры узлов UPDL для квиза
   │  │  │     │  ├─ ActionHandler.ts    # Обработка узлов Action
   │  │  │     │  ├─ CameraHandler.ts    # Обработка узлов Camera
   │  │  │     │  ├─ ComponentHandler.ts # Обработка узлов Component
   │  │  │     │  ├─ DataHandler.ts      # Обработка Data/Вопросов
   │  │  │     │  ├─ EntityHandler.ts    # Обработка узлов Entity
   │  │  │     │  ├─ EventHandler.ts     # Обработка узлов Event
   │  │  │     │  ├─ LightHandler.ts     # Обработка узлов Light
   │  │  │     │  ├─ ObjectHandler.ts    # Обработка узлов Object
   │  │  │     │  ├─ SpaceHandler.ts     # Обработка узлов Space
   │  │  │     │  ├─ UniversoHandler.ts  # Обработка узлов Universo
   │  │  │     │  └─ index.ts            # Экспорт обработчиков
   │  │  │     ├─ utils/                 # Утилиты, специфичные для шаблона
   │  │  │     │  └─ SimpleValidator.ts  # Утилиты валидации
   │  │  │     └─ index.ts               # Экспорт Quiz AR.js
   │  │  └─ mmoomm/        # Шаблон MMOOMM для MMO игр
   │  │     └─ playcanvas/ # PlayCanvas реализация шаблона MMOOMM
   │  │        ├─ PlayCanvasBuilder.ts       # Высокоуровневый PlayCanvas билдер
   │  │        ├─ PlayCanvasMMOOMMBuilder.ts # Реализация шаблона MMOOMM
   │  │        ├─ config.ts                  # Конфигурация шаблона MMOOMM
   │  │        ├─ handlers/                  # Процессоры узлов UPDL для MMOOMM
   │  │        │  ├─ ActionHandler.ts        # Обработка узлов Action
   │  │        │  ├─ ComponentHandler.ts     # Обработка узлов Component
   │  │        │  ├─ DataHandler.ts          # Обработка узлов Data
   │  │        │  ├─ EntityHandler.ts        # Обработка узлов Entity
   │  │        │  ├─ EventHandler.ts         # Обработка узлов Event
   │  │        │  ├─ SpaceHandler.ts         # Обработка узлов Space
   │  │        │  ├─ UniversoHandler.ts      # Обработка узлов Universo
   │  │        │  └─ index.ts                # Экспорт обработчиков
   │  │        └─ index.ts                   # Экспорт MMOOMM PlayCanvas
   │  └─ index.ts          # Экспорт основных билдеров
   ├─ components/          # Презентационные React-компоненты
   ├─ features/            # Функциональные модули для различных технологий
   │  ├─ arjs/             # Компоненты и логика для AR.js
   │  └─ playcanvas/       # Компоненты и логика для PlayCanvas
   ├─ pages/               # Компоненты страниц
   │  ├─ public/           # Публичные страницы (ARViewPage, PlayCanvasViewPage)
   │  └─ ...
   └─ index.ts             # Точка входа
```

**Система типов**: Типы UPDL импортируются из пакета `@universo/publish-srv`, обеспечивая централизованное определение типов и консистентность между фронтендом и бэкендом.

## Критичная архитектура: рендеринг в Iframe

**ВАЖНО**: Контент AR.js и PlayCanvas должен рендериться с использованием iframe для корректной загрузки библиотек и выполнения скриптов.

### Почему Iframe необходим

Библиотеки требуют правильного контекста выполнения скриптов, который `dangerouslySetInnerHTML` в React предоставить не может:

-   **Изоляция скриптов**: Iframe создает изолированный контекст выполнения.
-   **Загрузка библиотек**: Обеспечивает корректную загрузку JavaScript-библиотек.
-   **Совместимость с браузером**: Предотвращает конфликты с виртуальным DOM React.
-   **Безопасность**: Изолирует код от основного контекста приложения.

### Паттерн реализации (ARViewPage.tsx, PlayCanvasViewPage.tsx)

```typescript
// ❌ НЕПРАВИЛЬНО: dangerouslySetInnerHTML (скрипты не выполняются)
;<div dangerouslySetInnerHTML={{ __html: html }} />

// ✅ ПРАВИЛЬНО: подход с iframe (полное выполнение скриптов)
const iframe = document.createElement('iframe')
iframe.style.width = '100%'
iframe.style.height = '100%'
iframe.style.border = 'none'
container.appendChild(iframe)

const iframeDoc = iframe.contentDocument
iframeDoc.open()
iframeDoc.write(html) // HTML с тегами <script>
iframeDoc.close()
```

### Интеграция статических библиотек

Фронтенд работает с локальными библиотеками, которые раздаются непосредственно основным сервером Flowise:

#### Конфигурация сервера (packages/server/src/index.ts)

```typescript
// Статические ресурсы раздаются основным сервером Flowise
const publishFrtAssetsPath = path.join(__dirname, '../../../apps/publish-frt/base/dist/assets')
this.app.use('/assets', express.static(publishFrtAssetsPath))
```

#### Источники библиотек

-   **Локальный (Kiberplano)**: `/assets/libs/aframe/1.7.1/aframe.min.js` - раздается основным сервером.
-   **Официальный (CDN)**: `https://aframe.io/releases/1.7.1/aframe.min.js` - внешний CDN.

#### Преимущества

-   **Решение проблемы блокировки CDN**: Локальные библиотеки работают в регионах с ограничениями.
-   **Единый сервер**: Не требуется отдельный сервер для статических файлов.
-   **Производительность**: Прямая раздача из основного инстанса Flowise.
-   **Поддержка**: Библиотеки поставляются вместе с дистрибутивом фронтенда.

## Архитектура билдеров на основе шаблонов

Система билдеров была переработана в **модульную, основанную на шаблонах архитектуру**. Это обеспечивает максимальную гибкость и расширяемость для конвертации пространств UPDL в различные целевые платформы (AR.js, PlayCanvas и т.д.).

#### Ключевые компоненты

-   **`AbstractTemplateBuilder`**: Новый абстрактный базовый класс, который должны наследовать все шаблоны (например, для квизов AR.js, сцен PlayCanvas). Он предоставляет общую функциональность, такую как управление библиотеками и обертка структуры документа.
-   **`TemplateRegistry`**: Центральный реестр для управления и создания экземпляров различных билдеров шаблонов.
-   **`ARJSBuilder` / `PlayCanvasBuilder`**: Высокоуровневые билдеры, которые теперь выступают в роли контроллеров. Они определяют требуемый шаблон и делегируют весь процесс сборки соответствующему билдеру шаблона из реестра.
-   **`ARJSQuizBuilder` / `PlayCanvasMMOOMMBuilder`**: Конкретные реализации шаблонов. Они содержат собственный набор `обработчиков` для обработки различных узлов UPDL.
-   **`PlayCanvasMMOOMMBuilder`**: Конкретная реализация шаблона для генерации сцен PlayCanvas MMOOMM с MMO-специфичной функциональностью.
-   **`Обработчики (Handlers)`**: Специализированные процессоры для различных типов узлов UPDL теперь инкапсулированы внутри каждого шаблона (например, `builders/templates/quiz/arjs/handlers/`, `builders/templates/mmoomm/playcanvas/handlers/`). Это делает каждый шаблон самодостаточным.

#### Архитектура с приоритетом шаблонов

Новая архитектура организует код по принципу **сначала шаблон, затем технология**:

```
builders/templates/
├─ quiz/                    # Шаблон образовательного квиза
│  └─ arjs/                 # AR.js реализация
│     ├─ ARJSBuilder.ts     # Высокоуровневый контроллер
│     ├─ ARJSQuizBuilder.ts # Реализация шаблона
│     └─ handlers/          # Процессоры для квиза
└─ mmoomm/                  # Шаблон MMO игр
   └─ playcanvas/           # PlayCanvas реализация
      ├─ PlayCanvasBuilder.ts       # Высокоуровневый контроллер
      ├─ PlayCanvasMMOOMMBuilder.ts # Реализация шаблона
      └─ handlers/                  # Процессоры для MMOOMM
```

#### Возможности

-   **Максимальная расширяемость**: Легко добавлять новые целевые платформы (например, Three.js), создавая новую реализацию шаблона под существующими папками шаблонов.
-   **Повторное использование шаблонов**: Один и тот же шаблон (например, `quiz`) может поддерживать несколько технологий (AR.js, PlayCanvas и т.д.) с общей абстрактной логикой.
-   **Четкое разделение ответственности**: Высокоуровневые билдеры являются простыми контроллерами, в то время как реализации шаблонов содержат всю специфическую логику.
-   **Самодостаточные шаблоны**: Каждый шаблон объединяет свою собственную логику, обработчики и требуемые библиотеки, предотвращая конфликты.
-   **Типобезопасность**: Полная поддержка TypeScript с надежными интерфейсами (`ITemplateBuilder`, `TemplateConfig`).
-   **Общая функциональность**: Общая логика обрабатывается абстрактным базовым классом, что сокращает дублирование кода.
-   **Готовность к будущему**: Архитектура поддерживает неограниченные комбинации шаблонов и технологий.

#### Недавние улучшения

-   **Рефакторинг PlayCanvasViewPage**: Мигрировано с прямого импорта `PlayCanvasMMOOMMBuilder` на использование `TemplateRegistry`, что обеспечивает динамический выбор шаблона через параметр `config.templateId`.
-   **Флаг ENABLE_BACKEND_FETCH**: Добавлен флаг функции (по умолчанию: false) для опционального получения данных с бэкенда. При отключении компонент ожидает данные через props, что улучшает безопасность и надежность.
-   **Логика эксклюзивной публикации**: Исправлена логика в `PublicationApi.savePublicationSettings()` для воздействия только на поддерживаемые технологии (`chatbot`, `arjs`, `playcanvas`) и предотвращения случайного изменения других свойств конфигурации.
-   **Улучшение локализации**: Добавлены недостающие ключи перевода `publish.playcanvas.loading` для улучшенной многоязычной поддержки.

#### Использование AR.js Builder

```typescript
import { ARJSBuilder } from './builders'

const builder = new ARJSBuilder()

// Сборка с использованием шаблона по умолчанию 'quiz'
const result = await builder.buildFromFlowData(flowDataString, {
    projectName: 'Мой AR-опыт',
    markerType: 'preset',
    markerValue: 'hiro',
    libraryConfig: {
        arjs: { version: '3.4.7', source: 'kiberplano' },
        aframe: { version: '1.7.1', source: 'official' }
    }
})

// Или указание другого шаблона, если он доступен
const anotherResult = await builder.buildFromFlowData(flowDataString, {
    templateId: 'another-template'
    // ... другие опции
})

console.log(result.html) // Сгенерированный HTML для AR.js
console.log(result.metadata) // Метаданные сборки
```

### Использование PlayCanvas Builder

```typescript
import { PlayCanvasBuilder } from './builders'

const builder = new PlayCanvasBuilder()
const result = await builder.buildFromFlowData(flowDataString, {
    projectName: 'MMOOMM Демо',
    templateId: 'mmoomm'
})

console.log(result.html) // HTML для PlayCanvas
```

### Пример Universo MMOOMM

Шаблон `mmoomm` создает небольшую демо-сцену с кораблем, астероидами и воротами.
Выберите **PlayCanvas MMOOMM Template** в конфигурации или передайте `templateId: 'mmoomm'` при использовании билдера.
Опубликуйте проект и откройте публичную ссылку, чтобы исследовать прототип MMO-окружения.

## Архитектура обработки UPDL

Фронтенд теперь включает независимые возможности обработки UPDL через класс `UPDLProcessor`, что устраняет зависимости от утилит бэкенда.

### Ключевые компоненты

-   **UPDLProcessor**: Центральный класс для обработки потоков UPDL (мигрировал из `packages/server/src/utils/buildUPDLflow.ts`).
-   **Импорт типов**: Типы UPDL импортируются из пакета `@universo/publish-srv`.
-   **Независимость фронтенда**: Полная обработка UPDL на фронтенде без зависимостей от бэкенда.

### Возможности

-   **Анализ потока**: Идентифицирует узлы UPDL и конечные узлы.
-   **Обработка цепочек Space**: Обрабатывает сценарии с несколькими пространствами и последовательности сцен.
-   **Интеграция данных**: Обрабатывает узлы Data, связанные с пространствами.
-   **Связи объектов**: Сопоставляет узлы Object с узлами Data.
-   **Типобезопасность**: Полная поддержка TypeScript с централизованными определениями типов.

### Использование

```typescript
import { UPDLProcessor } from './builders/common/UPDLProcessor'
import { IUPDLSpace, IUPDLMultiScene } from '@universo/publish-srv'

const result = UPDLProcessor.processFlowData(flowDataString)
if (result.multiScene) {
    // Обработка сценария с несколькими пространствами
} else if (result.updlSpace) {
    // Обработка сценария с одним пространством
}
```

## Система конфигурации библиотек

Выбираемые пользователем источники библиотек для AR.js и A-Frame для решения проблем с блокировкой CDN.

### Как это работает

Пользователи могут выбирать источники библиотек через UI:

1.  **Конфигурация AR.js**:

    -   Версия: В настоящее время поддерживается 3.4.7
    -   Источник: "Официальный сервер" (CDN) или "Сервер Kiberplano" (локальный)

2.  **Конфигурация A-Frame**:
    -   Версия: В настоящее время поддерживается 1.7.1
    -   Источник: "Официальный сервер" (CDN) или "Сервер Kiberplano" (локальный)

### Источники библиотек

-   **Официальный сервер**: Внешние источники CDN

    -   A-Frame: `https://aframe.io/releases/1.7.1/aframe.min.js`
    -   AR.js: `https://raw.githack.com/AR-js-org/AR.js/3.4.7/aframe/build/aframe-ar.js`

-   **Сервер Kiberplano**: Локальный сервер (решает проблему блокировки CDN)
    -   A-Frame: `/assets/libs/aframe/1.7.1/aframe.min.js`
    -   AR.js: `/assets/libs/arjs/3.4.7/aframe-ar.js`

### Хранение конфигурации

Предпочтения по библиотекам хранятся в Supabase в `chatbotConfig.arjs.libraryConfig`:

```json
{
    "arjs": {
        "libraryConfig": {
            "arjs": { "version": "3.4.7", "source": "kiberplano" },
            "aframe": { "version": "1.7.1", "source": "official" }
        }
    }
}
```

### Преимущества

-   **Решает проблему блокировки CDN**: Пользователи в регионах с ограничениями могут использовать локальные библиотеки.
-   **Выбор пользователя**: Каждый пользователь сам определяет предпочитаемый источник библиотек.
-   **Постоянные настройки**: Конфигурация сохраняется для каждого чат-флоу.
-   **Обратная совместимость**: Существующие флоу продолжают работать с настройками по умолчанию.
-   **Будущая расширяемость**: Легко добавлять новые версии библиотек.

## Интеграция с бэкендом

Приложение поддерживает модульную архитектуру с четким разделением между компонентами фронтенда и бэкенда.

### Текущая архитектура

-   **Обработка на фронтенде**: Обработка потоков UPDL выполняется классом `UPDLProcessor` на фронтенде.
-   **API-взаимодействие**: Взаимодействие с бэкендом осуществляется исключительно через REST API с использованием клиентов из директории `api/`.
-   **Общие типы**: Типы UPDL централизованы в пакете `@universo/publish-srv` и импортируются фронтендом.
-   **Сервисный слой**: Бэкенд предоставляет `FlowDataService` для управления данными потоков.
-   **Независимость**: Отсутствие прямых импортов из `packages/server`, что обеспечивает полную модульную независимость.

### Рабочий процесс обработки потока

1.  **Запрос с фронтенда**: Пользователь инициирует публикацию через компонент `ARJSPublisher` или `PlayCanvasPublisher`.
2.  **Вызов API**: Фронтенд отправляет запрос на эндпоинт `/api/v1/publish/arjs` (или другой, специфичный для технологии).
3.  **Обработка на бэкенде**: `FlowDataService` извлекает данные потока из базы данных Flowise.
4.  **Обработка на фронтенде**: `UPDLProcessor` анализирует и конвертирует данные потока в структуры UPDL.
5.  **Генерация билдером**: Высокоуровневый билдер (`ARJSBuilder`, `PlayCanvasBuilder`) делегирует процесс сборки зарегистрированному билдеру шаблона (например, `ARJSQuizBuilder`, `PlayCanvasMMOOMMBuilder`), который конвертирует пространство UPDL в целевой формат.
6.  **Результат**: Сгенерированный контент раздается через публичные URL с рендерингом в iframe.

### Преимущества миграции

-   **Производительность**: Обработка на фронтенде снижает нагрузку на бэкенд.
-   **Модульность**: Четкое разделение ответственности между фронтендом и бэкендом.
-   **Типобезопасность**: Централизованные определения типов предотвращают несоответствия.
-   **Масштабируемость**: Фронтенд может независимо обрабатывать сложные потоки UPDL.
-   **Поддержка**: Упрощенная архитектура с меньшим количеством межпакетных зависимостей.

### Интеграция с системой ботов

Это фронтенд-приложение тесно интегрировано с основной системой публикации ботов, расположенной в `packages/ui/src/views/publish/bots/`:

-   **Интеграция конфигурации**: Публикатор AR.js доступен через основной интерфейс публикации в системе ботов.
-   **Общее состояние публикации**: Настройки публикации хранятся в Supabase с использованием той же структуры `chatbotConfig`, что и основная система ботов.
-   **Конфигурация для конкретной технологии**: Настройки AR.js и PlayCanvas хранятся в своих блоках (`arjs`, `playcanvas`) внутри `chatbotConfig`, сохраняя разделение с настройками чат-бота.
-   **Согласованность маршрутов API**: Используются те же маршруты API Flowise (`/api/v1/uniks/{unikId}/chatflows/{chatflowId}`), что и основная система.

### Интеграция с Supabase

Сохранение состояния публикации обрабатывается через интеграцию с Supabase:

-   **Структура для нескольких технологий**: Настройки хранятся в поле `chatbotConfig` со структурой `{"chatbot": {...}, "arjs": {...}, "playcanvas": {...}}`.
-   **Независимые состояния публикации**: Каждая технология (чат-бот, AR.js, PlayCanvas) имеет свой собственный флаг `isPublic`.
-   **Эксклюзивная публикация**: Система гарантирует, что только одна технология может быть публичной в один момент времени. Если одна включается, все остальные автоматически отключаются.
-   **Автосохранение**: Настройки автоматически сохраняются при изменении параметров.
-   **Восстановление состояния**: Предыдущие настройки восстанавливаются при монтировании компонента.
-   **Глобальный статус публикации**: Общий флаг `isPublic` устанавливается в true, если любая из технологий является публичной.

#### Логика эксклюзивной публикации

Система реализует эксклюзивную публикацию: только одна технология может быть публичной одновременно.
При включении публикации для одной технологии (AR.js, PlayCanvas, Chatbot),
все другие технологии автоматически отключаются. Это обеспечивает четкую доставку контента
и предотвращает конфликты между различными режимами публикации.

## Основные компоненты

-   `UPDLProcessor` - Центральный класс для обработки потоков UPDL.
-   `ARJSPublisher` - Компонент для публикации проектов AR.js.
-   `ARJSExporter` - Демо-компонент для экспорта кода AR.js.
-   `ARViewPage` - Компонент страницы для просмотра AR-пространства.
-   `ARJSBuilder` - Высокоуровневый контроллер, который делегирует работу системе шаблонов.
-   `ARJSQuizBuilder` - Конкретная реализация шаблона для квизов AR.js.
-   `PlayCanvasPublisher` - Компонент для настроек публикации PlayCanvas.
-   `PlayCanvasBuilder` - Билдер для вывода HTML PlayCanvas с поддержкой шаблонов.
-   `PlayCanvasViewPage` - Компонент страницы для просмотра сцен PlayCanvas.
-   `PlayCanvasMMOOMMBuilder` - Конкретная реализация шаблона для проекта Universo MMOOMM.

## Архитектура API

Приложение использует модульную архитектуру API, организованную по слоям:

#### Основные утилиты API (`api/common.ts`)

-   `getAuthHeaders()` - Управление токенами аутентификации из localStorage.
-   `getCurrentUrlIds()` - Извлечение `unikId` и `chatflowId` из URL.
-   `getApiBaseUrl()` - Динамическое разрешение базового URL API.

#### Слой API публикации (`api/publication/`)

-   **`PublicationApi`** - Базовый класс для функциональности публикации для всех технологий. Управляет настройками для нескольких технологий в `chatbotConfig`.
-   **`ARJSPublicationApi`** - Управление настройками публикации для AR.js (наследует `PublicationApi`).
-   **`PlayCanvasPublicationApi`** - Управление настройками публикации для PlayCanvas (наследует `PublicationApi`).
-   **`StreamingPublicationApi`** - Генерация контента в реальном времени и потоковая публикация.

#### Возможности интеграции API

-   **Поддержка нескольких технологий**: API публикации разработано для поддержки AR.js, PlayCanvas, Chatbot и будущих технологий.
-   **Интеграция с Supabase**: Постоянное хранение с использованием структуры `chatbotConfig` с блоками для конкретных технологий.
-   **Обратная совместимость**: Включает псевдонимы совместимости (`ChatflowsApi`, `ARJSPublishApi`) для плавной миграции.
-   **Правильная аутентификация**: Использует правильные маршруты Flowise с `unikId` и заголовками `x-request-from: internal`.
-   **Предотвращение циклических зависимостей**: Чистая архитектура с утилитами `common.ts` для предотвращения циклов импорта.

## Создание AR.js-квизов с помощью UPDL

AR-квизы строятся с использованием цепочки узлов **Space** из UPDL. Каждое пространство может включать узлы **Data** с вопросами. К вопросу может быть подключено несколько узлов **Data** с ответами. Правильные ответы помечаются `isCorrect`, а узлы ответов также могут определять `enablePoints` и `pointsValue` для системы подсчета очков. Каждый узел ответа может быть связан с узлом **Object**, который появляется при выборе ответа.

Пространства могут образовывать последовательность через их соединение `nextSpace` для создания квизов с несколькими вопросами. Пространство без узлов Data может собирать информацию о пользователе (`collectName`, `collectEmail`, `collectPhone`) и сохранять ее в лиды Supabase. Последнее пространство в цепочке может включать `showPoints` для отображения счета участника. В настоящее время этот счет хранится в поле `lead.phone` в качестве временного решения.

## Рабочий процесс

Реализация использует потоковую генерацию для AR.js из узлов UPDL с сохранением конфигурации:

1.  Настройки автоматически загружаются из Supabase при монтировании компонента.
2.  Пользователь настраивает параметры проекта (название, маркер, источники библиотек) - настройки автоматически сохраняются.
3.  Пользователь переключает "Сделать публичным" - это запускает публикацию и сохраняет состояние в Supabase.
4.  Компонент `ARJSPublisher` отправляет POST-запрос на `/api/v1/publish/arjs` с `chatflowId` и выбранными опциями.
5.  Обработчик бэкенда `PublishController.publishARJS` возвращает ответ с `publicationId` и метаданными публикации.
6.  При доступе к публичному URL (`/p/{publicationId}`) рендерится компонент `PublicFlowView`, который затем определяет технологию и рендерит соответствующий просмотрщик (`ARViewPage` или `PlayCanvasViewPage`).
7.  Компонент страницы делает GET-запрос на `/api/v1/publish/arjs/public/:publicationId` (или аналогичный для других технологий), который возвращает данные потока с бэкенда.
8.  `UPDLProcessor` анализирует данные потока и конвертирует их в структуры UPDL на фронтенде.
9.  Соответствующая система билдера (`ARJSBuilder`, `PlayCanvasBuilder`) конвертирует пространство UPDL в рендерящиеся элементы, используя правильный шаблон.
10. **Критично**: Сгенерированный HTML рендерится в iframe для правильного выполнения скриптов и загрузки библиотек.

## Настройка и разработка

Для запуска проекта:

```bash
pnpm run dev
```

Для сборки:

```bash
pnpm run build
```

## Процесс сборки

Процесс сборки включает два этапа:

1.  **Компиляция TypeScript**: Компилирует файлы TypeScript в JavaScript.
2.  **Задачи Gulp**: Копирует статические ресурсы (SVG, PNG, JSON, CSS, JS-библиотеки) в папку `dist`.

### Доступные скрипты

-   `pnpm clean` - Очистить директорию `dist`.
-   `pnpm build` - Собрать пакет (TypeScript + Gulp).
-   `pnpm dev` - Режим отслеживания для разработки.
-   `pnpm lint` - Проверить исходный код линтером.

### Задачи Gulp

Процесс Gulp копирует все статические файлы (SVG, PNG, JPG, JSON, CSS, JS) из исходных директорий в папку `dist`, сохраняя структуру директорий. Это обеспечивает доступность ресурсов и локальных библиотек во время выполнения.

## Зависимости

Убедитесь, что вы установили зависимости из корня проекта, используя:

```bash
pnpm install
```

## Разработка

При добавлении новых компонентов или страниц следуйте этим практикам:

1.  Создавайте компоненты в соответствующей директории.
2.  Используйте интерфейсы TypeScript для пропсов и состояния.
3.  Добавляйте соответствующие статические ресурсы в ту же папку (они будут скопированы во время сборки).
4.  Реализуйте поддержку интернационализации с помощью системы i18n.
5.  **Для контента AR.js/PlayCanvas**: Всегда используйте подход с iframe для правильного выполнения скриптов.

## Демо-режим

Для тестирования и демонстрации компонент `ARJSPublisher` имеет `DEMO_MODE`, который можно активировать, установив константу `DEMO_MODE = true`. В этом режиме:

1.  Отображается выбор шаблона (в настоящее время только один демо-шаблон "Quiz").
2.  Во время публикации не делаются реальные запросы к API.
3.  Предоставляется фиксированный URL публикации.
4.  Все взаимодействия с UI работают, но без реальных операций на сервере.
5.  Интеграция с Supabase отключена.

## Текущие ограничения

-   Нет поддержки офлайн-режима или кэширования пространств для повторного использования.
-   Нет оптимизации для мобильных устройств.
-   Вкладка "Экспорт" является только демонстрационной, без полной функциональности экспорта HTML/ZIP.

---

_Universo Platformo | Модуль фронтенда публикации_
