# PlayCanvas Scripts System for MMOOMM Template

This directory contains the modular PlayCanvas scripts system for the MMOOMM template, providing reusable and extensible script components for virtual world behaviors.

## Architecture Overview

The scripts system follows a modular architecture with the following key components:

### Core Components

- **`BaseScript`** - Abstract base class for all PlayCanvas scripts
- **`ScriptRegistry`** - Singleton registry for managing script instances
- **`ScriptUtils`** - Utility functions for script operations
- **`ScriptHandler`** - UPDL node processor for Script nodes

### Built-in Scripts

- **`RotatorScript`** - Smooth rotation animation script (extracted from PlayCanvasMMOOMMBuilder)

## Usage Examples

### Basic Rotation Script

```typescript
import { RotatorScript } from './scripts'

// Create a Y-axis rotator with default speed (20 deg/sec)
const rotator = RotatorScript.createYAxisRotator()

// Create a custom rotator
const customRotator = new RotatorScript({
    speed: 45,
    axis: 'z',
    direction: 'counterclockwise'
})
```

### Using Script Registry

```typescript
import { ScriptRegistry, RotatorScript } from './scripts'

const registry = ScriptRegistry.getInstance()

// Register a custom script
registry.register(new RotatorScript({ speed: 30 }), 'animation', false)

// Get a script by name
const script = registry.get('rotator')

// Generate script code
const result = registry.generateScript('rotator', options)
```

### UPDL Script Nodes

Script nodes in UPDL can be processed by the ScriptHandler:

```json
{
  "type": "Script",
  "data": {
    "scriptType": "rotator",
    "parameters": {
      "speed": 25,
      "axis": "y",
      "direction": "clockwise"
    },
    "enabled": true
  }
}
```

## Integration with PlayCanvasMMOOMMBuilder

The system is fully integrated with the existing MMOOMM builder:

1. **Automatic Initialization** - Script system is initialized in the builder constructor
2. **UPDL Support** - Script nodes are processed alongside other node types
3. **Backward Compatibility** - Existing rotation functionality preserved
4. **Enhanced Features** - New scripts can be easily added and configured

## File Structure

```
scripts/
├── BaseScript.ts          # Abstract base class
├── RotatorScript.ts       # Rotation animation script
├── ScriptRegistry.ts      # Script management registry
├── ScriptUtils.ts         # Utility functions
├── ScriptHandler.ts       # UPDL node processor
├── types.ts              # Type definitions
├── setup.ts              # System initialization
├── index.ts              # Main exports
└── README.md             # This documentation
```

## Benefits

### ✅ Modularity
- Scripts are separated into individual modules
- Easy to add new script types
- Clear separation of concerns

### ✅ Reusability
- Scripts can be reused across different entities
- Parameterizable behavior
- Registry-based management

### ✅ Extensibility
- Easy to add new script types
- Plugin-like architecture
- UPDL integration ready

### ✅ Backward Compatibility
- Existing functionality preserved
- Gradual migration path
- No breaking changes

## Future Enhancements

The system is designed to support future enhancements:

- **Movement Scripts** - Entity movement behaviors
- **Interaction Scripts** - User interaction handlers
- **Physics Scripts** - Physics-based behaviors
- **AI Scripts** - NPC and AI behaviors
- **Custom Scripts** - User-defined script types

## Development Guidelines

When creating new scripts:

1. Extend `BaseScript` abstract class
2. Implement required methods (`getMetadata`, `generateScript`)
3. Add proper parameter validation
4. Register in `setup.ts` if built-in
5. Add TypeScript types in `types.ts`
6. Update documentation

## Testing

The system has been tested with:
- ✅ TypeScript compilation
- ✅ Build process integration
- ✅ Backward compatibility with existing rotation functionality
- ✅ UPDL node processing integration

---

*Generated by Universo Platformo - PlayCanvas Scripts System v1.0.0*
