// Universo Platformo | Rotator PlayCanvas Script
// Smooth rotation animation script for entities

import { BaseScript, ScriptMetadata, ScriptParameters } from './BaseScript'
import { RotationParameters, RotationAxis, RotationDirection } from './types'
import { BuildOptions } from '../../../../common/types'

/**
 * Rotator script for smooth entity rotation animation
 * Extracted from PlayCanvasMMOOMMBuilder for better modularity
 */
export class RotatorScript extends BaseScript {
    private speed: number
    private axis: RotationAxis
    private direction: RotationDirection

    constructor(parameters: Partial<RotationParameters> = {}) {
        const defaultParams: RotationParameters = {
            speed: 20, // degrees per second
            axis: 'y',
            direction: 'clockwise'
        }

        super({ ...defaultParams, ...parameters })
        
        this.speed = this.parameters.speed as number
        this.axis = this.parameters.axis as RotationAxis
        this.direction = this.parameters.direction as RotationDirection
    }

    /**
     * Get script metadata
     */
    getMetadata(): ScriptMetadata {
        return {
            name: 'rotator',
            description: 'Smooth rotation animation script',
            version: '1.0.0',
            author: 'Universo Platformo',
            category: 'animation'
        }
    }

    /**
     * Validate rotation parameters
     */
    validateParameters(params: ScriptParameters): boolean {
        if (params.speed !== undefined) {
            if (typeof params.speed !== 'number' || params.speed < 0) {
                console.warn('Rotator speed must be a positive number')
                return false
            }
        }

        if (params.axis !== undefined) {
            if (!['x', 'y', 'z'].includes(params.axis)) {
                console.warn('Rotator axis must be x, y, or z')
                return false
            }
        }

        if (params.direction !== undefined) {
            if (!['clockwise', 'counterclockwise'].includes(params.direction)) {
                console.warn('Rotator direction must be clockwise or counterclockwise')
                return false
            }
        }

        return true
    }

    /**
     * Generate PlayCanvas script code
     */
    generateScript(options?: BuildOptions): string {
        const rotationVector = this.getRotationVector()
        const speedMultiplier = this.direction === 'counterclockwise' ? -1 : 1
        const finalSpeed = this.speed * speedMultiplier

        return `
// Rotator Script - Smooth rotation animation
const RotatorScript = pc.createScript('rotator');

// Script attributes (can be configured in PlayCanvas Editor)
RotatorScript.attributes.add('speed', {
    type: 'number',
    default: ${this.speed},
    title: 'Rotation Speed',
    description: 'Rotation speed in degrees per second'
});

RotatorScript.attributes.add('axis', {
    type: 'string',
    default: '${this.axis}',
    title: 'Rotation Axis',
    description: 'Axis of rotation (x, y, or z)'
});

RotatorScript.attributes.add('direction', {
    type: 'string',
    default: '${this.direction}',
    title: 'Rotation Direction',
    description: 'Direction of rotation (clockwise or counterclockwise)'
});

// Initialize script
RotatorScript.prototype.initialize = function() {
    // Cache rotation vector for performance
    this.rotationVector = this.getRotationVector();
    this.speedMultiplier = this.direction === 'counterclockwise' ? -1 : 1;
    this.finalSpeed = this.speed * this.speedMultiplier;
    
    console.log('[Rotator] Initialized on entity:', this.entity.name);
};

// Update method called every frame
RotatorScript.prototype.update = function(dt) {
    // Apply rotation based on delta time for frame-rate independent animation
    const rotationAmount = this.finalSpeed * dt;
    
    if (this.axis === 'x') {
        this.entity.rotate(rotationAmount, 0, 0);
    } else if (this.axis === 'y') {
        this.entity.rotate(0, rotationAmount, 0);
    } else if (this.axis === 'z') {
        this.entity.rotate(0, 0, rotationAmount);
    }
};

// Helper method to get rotation vector
RotatorScript.prototype.getRotationVector = function() {
    switch(this.axis) {
        case 'x': return new pc.Vec3(1, 0, 0);
        case 'y': return new pc.Vec3(0, 1, 0);
        case 'z': return new pc.Vec3(0, 0, 1);
        default: return new pc.Vec3(0, 1, 0);
    }
};

// Optional: Pause/Resume methods for external control
RotatorScript.prototype.pause = function() {
    this.enabled = false;
};

RotatorScript.prototype.resume = function() {
    this.enabled = true;
};`
    }

    /**
     * Get rotation vector based on axis
     */
    private getRotationVector(): { x: number; y: number; z: number } {
        switch (this.axis) {
            case 'x': return { x: 1, y: 0, z: 0 }
            case 'y': return { x: 0, y: 1, z: 0 }
            case 'z': return { x: 0, y: 0, z: 1 }
            default: return { x: 0, y: 1, z: 0 }
        }
    }

    /**
     * Create a rotator script with specific parameters
     * Static factory method for common use cases
     */
    static createYAxisRotator(speed: number = 20): RotatorScript {
        return new RotatorScript({ speed, axis: 'y', direction: 'clockwise' })
    }

    static createXAxisRotator(speed: number = 20): RotatorScript {
        return new RotatorScript({ speed, axis: 'x', direction: 'clockwise' })
    }

    static createZAxisRotator(speed: number = 20): RotatorScript {
        return new RotatorScript({ speed, axis: 'z', direction: 'clockwise' })
    }

    /**
     * Generate simple attachment code (backward compatibility)
     * This matches the original implementation from PlayCanvasMMOOMMBuilder
     */
    generateSimpleAttachment(entityVariableName: string): string {
        return `
// Add rotation script for smooth animation
const RotatorScript = pc.createScript('rotator');
RotatorScript.prototype.update = function(dt) {
    this.entity.rotate(0, ${this.speed} * dt, 0); // Rotate ${this.speed} degrees per second
};

${entityVariableName}.addComponent('script');
${entityVariableName}.script.create('rotator');`
    }
}
