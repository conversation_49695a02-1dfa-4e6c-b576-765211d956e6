// Universo Platformo | PlayCanvas Script Utilities
// Utility functions for working with PlayCanvas scripts

import { BaseScript, ScriptParameters } from './BaseScript'
import { ScriptGenerationResult, ScriptAttachment } from './types'
import { BuildOptions } from '../../../../common/types'

/**
 * Utility class for PlayCanvas script operations
 */
export class ScriptUtils {
    /**
     * Generate script attachment code for an entity
     * @param entityCode Existing entity creation code
     * @param scriptName Name of the script to attach
     * @param parameters Optional script parameters
     * @returns Modified entity code with script attachment
     */
    static attachScriptToEntity(
        entityCode: string,
        scriptName: string,
        parameters?: ScriptParameters
    ): string {
        const parameterCode = parameters 
            ? this.generateParameterAssignments(scriptName, parameters)
            : ''

        return `${entityCode}

// Attach ${scriptName} script
entity.addComponent('script');
entity.script.create('${scriptName}');${parameterCode}`
    }

    /**
     * Generate script definition code
     * @param script BaseScript instance
     * @param options Build options
     * @returns Script generation result
     */
    static generateScriptDefinition(
        script: BaseScript, 
        options?: BuildOptions
    ): ScriptGenerationResult {
        try {
            const scriptCode = script.generateDefinition(options)
            return {
                success: true,
                scriptCode
            }
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            }
        }
    }

    /**
     * Generate multiple script attachments for an entity
     * @param entityCode Existing entity creation code
     * @param attachments Array of script attachments
     * @returns Modified entity code with all script attachments
     */
    static attachMultipleScripts(
        entityCode: string,
        attachments: ScriptAttachment[]
    ): string {
        if (attachments.length === 0) {
            return entityCode
        }

        let result = `${entityCode}

// Attach scripts to entity
entity.addComponent('script');`

        attachments.forEach(attachment => {
            result += `
entity.script.create('${attachment.scriptName}');`
            
            if (attachment.parameters) {
                result += this.generateParameterAssignments(
                    attachment.scriptName, 
                    attachment.parameters
                )
            }
        })

        return result
    }

    /**
     * Validate script parameters against expected types
     * @param params Parameters to validate
     * @param expectedTypes Expected parameter types
     * @returns True if parameters are valid
     */
    static validateScriptParameters(
        params: ScriptParameters,
        expectedTypes: Record<string, string>
    ): boolean {
        for (const [key, expectedType] of Object.entries(expectedTypes)) {
            if (params[key] !== undefined) {
                const actualType = typeof params[key]
                if (actualType !== expectedType) {
                    console.warn(
                        `Parameter ${key} expected ${expectedType}, got ${actualType}`
                    )
                    return false
                }
            }
        }
        return true
    }

    /**
     * Generate parameter assignment code for a script
     * @param scriptName Name of the script
     * @param parameters Parameters to assign
     * @returns Parameter assignment code
     */
    private static generateParameterAssignments(
        scriptName: string,
        parameters: ScriptParameters
    ): string {
        let code = ''
        
        Object.entries(parameters).forEach(([key, value]) => {
            const serializedValue = this.serializeParameterValue(value)
            code += `
entity.script.${scriptName}.${key} = ${serializedValue};`
        })

        return code
    }

    /**
     * Serialize parameter value for code generation
     * @param value Parameter value
     * @returns Serialized value as string
     */
    private static serializeParameterValue(value: any): string {
        if (typeof value === 'string') {
            return `'${value.replace(/'/g, "\\'")}'`
        }
        if (typeof value === 'number' || typeof value === 'boolean') {
            return String(value)
        }
        if (typeof value === 'object' && value !== null) {
            return JSON.stringify(value)
        }
        return 'undefined'
    }

    /**
     * Generate script initialization code
     * @param scripts Array of scripts to initialize
     * @param options Build options
     * @returns Complete script initialization code
     */
    static generateScriptInitialization(
        scripts: BaseScript[],
        options?: BuildOptions
    ): string {
        const definitions = scripts
            .map(script => script.generateDefinition(options))
            .join('\n\n')

        return `
// PlayCanvas Scripts Initialization
${definitions}

console.log('[MMOOMM Scripts] Initialized ${scripts.length} script(s)');`
    }
}
