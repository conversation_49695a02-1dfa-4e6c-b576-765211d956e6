// Universo Platformo | PlayCanvas Scripts Types
// Type definitions for the PlayCanvas scripts system

import { BaseScript } from './BaseScript'

/**
 * Script configuration for UPDL Script nodes
 */
export interface ScriptConfig {
    scriptName: string
    parameters?: Record<string, any>
    enabled?: boolean
    priority?: number
}

/**
 * Script attachment configuration
 */
export interface ScriptAttachment {
    entityId: string
    scriptName: string
    parameters?: Record<string, any>
}

/**
 * Script registry entry
 */
export interface ScriptRegistryEntry {
    script: BaseScript
    category: string
    isBuiltIn: boolean
    registeredAt: Date
}

/**
 * Script generation result
 */
export interface ScriptGenerationResult {
    success: boolean
    scriptCode?: string
    attachmentCode?: string
    error?: string
}

/**
 * Rotation axis type for rotation scripts
 */
export type RotationAxis = 'x' | 'y' | 'z'

/**
 * Rotation direction type
 */
export type RotationDirection = 'clockwise' | 'counterclockwise'

/**
 * Common script parameter types
 */
export interface RotationParameters {
    speed: number
    axis: RotationAxis
    direction: RotationDirection
}

export interface MovementParameters {
    speed: number
    direction: { x: number; y: number; z: number }
    oscillate: boolean
}

export interface InteractionParameters {
    triggerDistance: number
    requireClick: boolean
    oneTime: boolean
}
