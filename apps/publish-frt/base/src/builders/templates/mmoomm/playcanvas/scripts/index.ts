// Universo Platformo | PlayCanvas Scripts Module
// Central export for all PlayCanvas scripts functionality

// Base classes and interfaces
export { BaseScript } from './BaseScript'
export type { ScriptParameters, ScriptMetadata } from './BaseScript'

// Type definitions
export type {
    ScriptConfig,
    ScriptAttachment,
    ScriptRegistryEntry,
    ScriptGenerationResult,
    RotationAxis,
    RotationDirection,
    RotationParameters,
    MovementParameters,
    InteractionParameters
} from './types'

// Core scripts
export { RotatorScript } from './RotatorScript'

// Registry and utilities
export { ScriptRegistry } from './ScriptRegistry'
export { ScriptUtils } from './ScriptUtils'

// Setup and initialization
export {
    initializeScriptRegistry,
    getDefaultRotatorScript,
    registerCustomScripts,
    setupScriptSystem
} from './setup'
