// Universo Platformo | PlayCanvas Scripts Setup
// Initialize and register built-in scripts for MMOOMM template

import { ScriptRegistry } from './ScriptRegistry'
import { RotatorScript } from './RotatorScript'

/**
 * Initialize the script registry with built-in scripts
 * This function should be called once during application startup
 */
export function initializeScriptRegistry(): void {
    const registry = ScriptRegistry.getInstance()

    // Register built-in animation scripts
    registry.register(
        new RotatorScript(),
        'animation',
        true // isBuiltIn
    )

    // Register common rotation variants
    registry.register(
        RotatorScript.createYAxisRotator(30),
        'animation',
        true
    )

    registry.register(
        RotatorScript.createXAxisRotator(15),
        'animation', 
        true
    )

    registry.register(
        RotatorScript.createZAxisRotator(45),
        'animation',
        true
    )

    console.log('[MMOOMM Scripts] Initialized script registry with built-in scripts')
    
    // Log registry statistics
    const stats = registry.getStats()
    console.log('[MMOOMM Scripts] Registry stats:', stats)
}

/**
 * Get the default rotator script for backward compatibility
 * This maintains compatibility with existing PlayCanvasMMOOMMBuilder code
 */
export function getDefaultRotatorScript(): RotatorScript {
    return RotatorScript.createYAxisRotator(20) // Matches original implementation
}

/**
 * Register custom scripts (for future extensibility)
 * This function can be extended to register additional custom scripts
 */
export function registerCustomScripts(): void {
    // Future: Register additional custom scripts here
    // const registry = ScriptRegistry.getInstance()
    // registry.register(new CustomScript(), 'custom', false)
}

/**
 * Setup complete script system
 * Call this function to initialize the entire script system
 */
export function setupScriptSystem(): void {
    initializeScriptRegistry()
    registerCustomScripts()
    
    console.log('[MMOOMM Scripts] Script system setup complete')
}
