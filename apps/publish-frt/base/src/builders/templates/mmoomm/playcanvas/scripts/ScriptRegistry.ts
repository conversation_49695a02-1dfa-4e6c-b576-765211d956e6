// Universo Platformo | PlayCanvas Script Registry
// Central registry for managing PlayCanvas scripts in MMOOMM template

import { BaseScript } from './BaseScript'
import { ScriptRegistryEntry, ScriptGenerationResult } from './types'
import { BuildOptions } from '../../../../common/types'

/**
 * Registry for managing PlayCanvas scripts
 * Provides centralized script management and instantiation
 */
export class ScriptRegistry {
    private static instance: ScriptRegistry
    private scripts: Map<string, ScriptRegistryEntry> = new Map()

    private constructor() {
        // Private constructor for singleton pattern
    }

    /**
     * Get singleton instance of ScriptRegistry
     */
    static getInstance(): ScriptRegistry {
        if (!ScriptRegistry.instance) {
            ScriptRegistry.instance = new ScriptRegistry()
        }
        return ScriptRegistry.instance
    }

    /**
     * Register a script in the registry
     * @param script Script instance to register
     * @param category Script category (e.g., 'animation', 'interaction', 'physics')
     * @param isBuiltIn Whether this is a built-in script
     */
    register(script: BaseScript, category: string = 'general', isBuiltIn: boolean = false): void {
        const scriptName = script.getName()
        
        if (this.scripts.has(scriptName)) {
            console.warn(`[ScriptRegistry] Script '${scriptName}' is already registered. Overwriting.`)
        }

        const entry: ScriptRegistryEntry = {
            script,
            category,
            isBuiltIn,
            registeredAt: new Date()
        }

        this.scripts.set(scriptName, entry)
        console.log(`[ScriptRegistry] Registered script '${scriptName}' in category '${category}'`)
    }

    /**
     * Get a script by name
     * @param name Script name
     * @returns Script instance or undefined if not found
     */
    get(name: string): BaseScript | undefined {
        const entry = this.scripts.get(name)
        return entry?.script
    }

    /**
     * Get script entry with metadata
     * @param name Script name
     * @returns Script registry entry or undefined if not found
     */
    getEntry(name: string): ScriptRegistryEntry | undefined {
        return this.scripts.get(name)
    }

    /**
     * Get all registered scripts
     * @returns Array of all script instances
     */
    getAll(): BaseScript[] {
        return Array.from(this.scripts.values()).map(entry => entry.script)
    }

    /**
     * Get scripts by category
     * @param category Category name
     * @returns Array of scripts in the specified category
     */
    getByCategory(category: string): BaseScript[] {
        return Array.from(this.scripts.values())
            .filter(entry => entry.category === category)
            .map(entry => entry.script)
    }

    /**
     * Get all available categories
     * @returns Array of unique category names
     */
    getCategories(): string[] {
        const categories = Array.from(this.scripts.values()).map(entry => entry.category)
        return [...new Set(categories)].sort()
    }

    /**
     * Check if a script is registered
     * @param name Script name
     * @returns True if script is registered
     */
    has(name: string): boolean {
        return this.scripts.has(name)
    }

    /**
     * Unregister a script
     * @param name Script name
     * @returns True if script was removed, false if not found
     */
    unregister(name: string): boolean {
        const removed = this.scripts.delete(name)
        if (removed) {
            console.log(`[ScriptRegistry] Unregistered script '${name}'`)
        }
        return removed
    }

    /**
     * Clear all scripts from registry
     * @param includeBuiltIn Whether to also clear built-in scripts
     */
    clear(includeBuiltIn: boolean = false): void {
        if (includeBuiltIn) {
            this.scripts.clear()
            console.log('[ScriptRegistry] Cleared all scripts')
        } else {
            const toRemove: string[] = []
            this.scripts.forEach((entry, name) => {
                if (!entry.isBuiltIn) {
                    toRemove.push(name)
                }
            })
            toRemove.forEach(name => this.scripts.delete(name))
            console.log(`[ScriptRegistry] Cleared ${toRemove.length} non-built-in scripts`)
        }
    }

    /**
     * Generate script definition code for a specific script
     * @param scriptName Name of the script
     * @param options Build options
     * @returns Script generation result
     */
    generateScript(scriptName: string, options?: BuildOptions): ScriptGenerationResult {
        const script = this.get(scriptName)
        if (!script) {
            return {
                success: false,
                error: `Script '${scriptName}' not found in registry`
            }
        }

        try {
            const scriptCode = script.generateDefinition(options)
            return {
                success: true,
                scriptCode
            }
        } catch (error) {
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            }
        }
    }

    /**
     * Generate all script definitions
     * @param options Build options
     * @returns Combined script definitions code
     */
    generateAllScripts(options?: BuildOptions): string {
        const scripts = this.getAll()
        if (scripts.length === 0) {
            return '// No scripts registered'
        }

        const definitions = scripts
            .map(script => {
                try {
                    return script.generateDefinition(options)
                } catch (error) {
                    console.error(`Error generating script ${script.getName()}:`, error)
                    return `// Error generating script ${script.getName()}: ${error}`
                }
            })
            .join('\n\n')

        return `
// PlayCanvas Scripts - Generated by ScriptRegistry
// Total scripts: ${scripts.length}

${definitions}

console.log('[ScriptRegistry] Loaded ${scripts.length} script(s)');`
    }

    /**
     * Get registry statistics
     * @returns Registry statistics object
     */
    getStats(): {
        totalScripts: number
        builtInScripts: number
        customScripts: number
        categories: string[]
        oldestScript?: Date
        newestScript?: Date
    } {
        const entries = Array.from(this.scripts.values())
        const builtInCount = entries.filter(entry => entry.isBuiltIn).length
        const dates = entries.map(entry => entry.registeredAt)

        return {
            totalScripts: entries.length,
            builtInScripts: builtInCount,
            customScripts: entries.length - builtInCount,
            categories: this.getCategories(),
            oldestScript: dates.length > 0 ? new Date(Math.min(...dates.map(d => d.getTime()))) : undefined,
            newestScript: dates.length > 0 ? new Date(Math.max(...dates.map(d => d.getTime()))) : undefined
        }
    }

    /**
     * Create a new script instance with parameters
     * @param scriptName Name of the script
     * @param parameters Parameters for the script
     * @returns New script instance or undefined if script not found
     */
    createInstance(scriptName: string, parameters?: Record<string, any>): BaseScript | undefined {
        const entry = this.scripts.get(scriptName)
        if (!entry) {
            return undefined
        }

        // Create a new instance with the same constructor as the registered script
        const ScriptClass = entry.script.constructor as new (params?: any) => BaseScript
        return new ScriptClass(parameters)
    }
}
