// Universo Platformo | Base PlayCanvas Script
// Abstract base class for all PlayCanvas scripts in MMOOMM template

import { BuildOptions } from '../../../../common/types'

/**
 * Script parameters interface for configuring script behavior
 */
export interface ScriptParameters {
    [key: string]: any
}

/**
 * Script metadata interface for script information
 */
export interface ScriptMetadata {
    name: string
    description: string
    version: string
    author?: string
    category?: string
}

/**
 * Abstract base class for all PlayCanvas scripts
 * Provides common functionality and enforces consistent interface
 */
export abstract class BaseScript {
    protected parameters: ScriptParameters
    protected metadata: ScriptMetadata

    constructor(parameters: ScriptParameters = {}) {
        this.parameters = parameters
        this.metadata = this.getMetadata()
    }

    /**
     * Get script metadata (name, description, etc.)
     */
    abstract getMetadata(): ScriptMetadata

    /**
     * Generate the PlayCanvas script code
     * @param options Build options from the template builder
     * @returns Generated PlayCanvas script code as string
     */
    abstract generateScript(options?: BuildOptions): string

    /**
     * Validate script parameters
     * @param params Parameters to validate
     * @returns True if parameters are valid
     */
    validateParameters(params: ScriptParameters): boolean {
        // Default implementation - can be overridden by subclasses
        return true
    }

    /**
     * Get script name
     */
    getName(): string {
        return this.metadata.name
    }

    /**
     * Get script description
     */
    getDescription(): string {
        return this.metadata.description
    }

    /**
     * Get script version
     */
    getVersion(): string {
        return this.metadata.version
    }

    /**
     * Get script parameters
     */
    getParameters(): ScriptParameters {
        return { ...this.parameters }
    }

    /**
     * Update script parameters
     * @param newParams New parameters to merge
     */
    updateParameters(newParams: ScriptParameters): void {
        if (this.validateParameters(newParams)) {
            this.parameters = { ...this.parameters, ...newParams }
        } else {
            throw new Error(`Invalid parameters for script ${this.getName()}`)
        }
    }

    /**
     * Generate script attachment code for an entity
     * @param entityVariableName Variable name of the entity in generated code
     * @returns Code to attach this script to the entity
     */
    generateAttachmentCode(entityVariableName: string): string {
        return `
// Attach ${this.getName()} script to entity
${entityVariableName}.addComponent('script');
${entityVariableName}.script.create('${this.getName()}');`
    }

    /**
     * Generate script definition code that should be included in the document
     * @param options Build options
     * @returns Complete script definition code
     */
    generateDefinition(options?: BuildOptions): string {
        const scriptCode = this.generateScript(options)
        return `
// ${this.getDescription()} (v${this.getVersion()})
${scriptCode}`
    }
}
