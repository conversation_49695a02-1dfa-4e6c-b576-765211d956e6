// Universo Platformo | MMOOMM Script Handler
// Handles Script nodes for PlayCanvas MMOOMM template

import { BuildOptions } from '../../../../common/types'
import { ScriptRegistry, ScriptUtils, RotatorScript } from '../scripts'
import { ScriptConfig, ScriptAttachment } from '../scripts/types'

export class ScriptHandler {
    private scriptRegistry: ScriptRegistry

    constructor() {
        this.scriptRegistry = ScriptRegistry.getInstance()
    }

    /**
     * Process Script nodes for MMOOMM environment
     * Script nodes can define reusable behaviors for entities
     */
    process(script: any, options: BuildOptions = {}): string {
        if (!script) return ''

        const scriptType = script.data?.scriptType || 'rotator'
        const scriptId = script.data?.id || `script_${Math.random().toString(36).substr(2, 9)}`
        const parameters = script.data?.parameters || {}
        const enabled = script.data?.enabled !== false

        if (!enabled) {
            return `// Script ${scriptId} (${scriptType}) - disabled\n`
        }

        return this.generateScriptCode(scriptType, scriptId, parameters, options)
    }

    /**
     * Process multiple Script nodes
     * @param scripts Array of script nodes
     * @param options Build options
     * @returns Combined script code
     */
    processMultiple(scripts: any[], options: BuildOptions = {}): string {
        if (!scripts || scripts.length === 0) {
            return ''
        }

        const scriptCodes = scripts
            .map(script => this.process(script, options))
            .filter(code => code.trim() !== '')

        if (scriptCodes.length === 0) {
            return ''
        }

        return `
// MMOOMM Scripts Processing
${scriptCodes.join('\n\n')}

console.log('[MMOOMM Scripts] Processed ${scriptCodes.length} script(s)');`
    }

    /**
     * Generate script code based on script type
     */
    private generateScriptCode(
        scriptType: string,
        scriptId: string,
        parameters: any,
        options: BuildOptions
    ): string {
        switch (scriptType) {
            case 'rotator':
                return this.generateRotatorScript(scriptId, parameters, options)
            case 'custom':
                return this.generateCustomScript(scriptId, parameters, options)
            default:
                return this.generateGenericScript(scriptType, scriptId, parameters, options)
        }
    }

    /**
     * Generate rotator script code
     */
    private generateRotatorScript(
        scriptId: string,
        parameters: any,
        options: BuildOptions
    ): string {
        const speed = parameters.speed || 20
        const axis = parameters.axis || 'y'
        const direction = parameters.direction || 'clockwise'

        const rotatorScript = new RotatorScript({ speed, axis, direction })
        const result = ScriptUtils.generateScriptDefinition(rotatorScript, options)

        if (!result.success) {
            return `// Error generating rotator script ${scriptId}: ${result.error}`
        }

        return `
// Rotator Script: ${scriptId}
${result.scriptCode}

// Script ready for attachment to entities
console.log('[MMOOMM Script] Rotator script ${scriptId} ready (speed: ${speed}, axis: ${axis})');`
    }

    /**
     * Generate custom script code from registry
     */
    private generateCustomScript(
        scriptId: string,
        parameters: any,
        options: BuildOptions
    ): string {
        const scriptName = parameters.scriptName
        if (!scriptName) {
            return `// Error: Custom script ${scriptId} missing scriptName parameter`
        }

        const script = this.scriptRegistry.get(scriptName)
        if (!script) {
            return `// Error: Script '${scriptName}' not found in registry for ${scriptId}`
        }

        // Create instance with custom parameters
        const scriptInstance = this.scriptRegistry.createInstance(scriptName, parameters)
        if (!scriptInstance) {
            return `// Error: Failed to create instance of script '${scriptName}' for ${scriptId}`
        }

        const result = ScriptUtils.generateScriptDefinition(scriptInstance, options)
        if (!result.success) {
            return `// Error generating custom script ${scriptId}: ${result.error}`
        }

        return `
// Custom Script: ${scriptId} (${scriptName})
${result.scriptCode}

console.log('[MMOOMM Script] Custom script ${scriptId} ready (${scriptName})');`
    }

    /**
     * Generate generic script code for unknown types
     */
    private generateGenericScript(
        scriptType: string,
        scriptId: string,
        parameters: any,
        options: BuildOptions
    ): string {
        // Try to find script in registry by type
        const script = this.scriptRegistry.get(scriptType)
        if (script) {
            const scriptInstance = this.scriptRegistry.createInstance(scriptType, parameters)
            if (scriptInstance) {
                const result = ScriptUtils.generateScriptDefinition(scriptInstance, options)
                if (result.success) {
                    return `
// Generic Script: ${scriptId} (${scriptType})
${result.scriptCode}

console.log('[MMOOMM Script] Generic script ${scriptId} ready (${scriptType})');`
                }
            }
        }

        // Fallback: generate placeholder
        return `
// Unknown Script Type: ${scriptId} (${scriptType})
// Script type '${scriptType}' not found in registry
console.warn('[MMOOMM Script] Unknown script type: ${scriptType} for ${scriptId}');`
    }

    /**
     * Generate script attachment code for entities
     * @param entityCode Existing entity code
     * @param scriptAttachments Array of script attachments
     * @returns Modified entity code with script attachments
     */
    generateScriptAttachments(
        entityCode: string,
        scriptAttachments: ScriptAttachment[]
    ): string {
        if (!scriptAttachments || scriptAttachments.length === 0) {
            return entityCode
        }

        return ScriptUtils.attachMultipleScripts(entityCode, scriptAttachments)
    }

    /**
     * Generate all registered scripts initialization
     * @param options Build options
     * @returns Script initialization code
     */
    generateAllScriptsInitialization(options: BuildOptions = {}): string {
        const allScripts = this.scriptRegistry.getAll()
        return ScriptUtils.generateScriptInitialization(allScripts, options)
    }

    /**
     * Get script registry statistics for debugging
     */
    getRegistryStats(): any {
        return this.scriptRegistry.getStats()
    }
}
