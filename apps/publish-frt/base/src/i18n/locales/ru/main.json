{"general": {"publish": "Опубликовать", "save": "Сохранить", "cancel": "Отмена", "delete": "Удалить", "edit": "Редактировать", "create": "Создать", "update": "Обновить", "loading": "Загрузка...", "success": "Успешно", "error": "Ошибка", "comingSoon": "скоро"}, "navigation": {"dashboard": "Панель управления", "projects": "Проекты", "settings": "Настройки", "help": "Помощь", "logout": "Выход"}, "projects": {"title": "Проекты", "create": "Создать проект", "edit": "Редактировать проект", "delete": "Удалить проект", "name": "Название проекта", "description": "Описание", "status": "Статус", "lastModified": "Последнее изменение", "createdAt": "Создан", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noProjects": "Проекты не найдены"}, "publishing": {"title": "Публикация", "platform": "Платформа", "version": "Версия", "visibility": "Видимость", "public": "Публичный", "private": "Приватный", "publishNow": "Опубликовать сейчас", "scheduling": "Запланировать публикацию", "scheduledFor": "Запланировано на", "confirmPublish": "Вы уверены, что хотите опубликовать?", "publishSuccess": "Успешно опубликовано", "publishError": "Не удалось опубликовать", "targetPlatforms": "Целевые платформы", "publishSettings": "Настройки публикации"}, "settings": {"title": "Настройки", "general": "Общие настройки", "account": "Настройки аккаунта", "notifications": "Уведомления", "appearance": "Вн<PERSON><PERSON>ний вид", "language": "Язык", "theme": "Тема", "dark": "Тёмная", "light": "Светлая", "system": "Системная"}, "validation": {"required": "Это поле обязательно", "minLength": "Должно быть не менее {{min}} символов", "maxLength": "Должно быть не более {{max}} символов", "invalidFormat": "Неверный формат"}, "errors": {"generic": "Что-то пошло не так", "network": "Ошибка сети", "unauthorized": "Нет доступа", "notFound": "Не найдено", "serverError": "Ошибка сервера"}, "publish": {"title": "Публикация и экспорт", "subtitle": "Поделитесь и экспортируйте ваши UPDL проекты", "general": {"cancel": "Отмена", "close": "Закрыть", "loading": "Загрузка...", "comingSoon": "скоро"}, "project": {"title": "Название проекта"}, "tabs": {"configuration": "Конфигурация", "publish": "Публикация", "export": "Экспорт"}, "configuration": {"title": "Конфигурация", "description": "Выберите режим для вашей публикации и настройте основные параметры", "chooseMode": "Выберите режим публикации", "makePublic": "Сделать публичным"}, "technology": {"label": "Технология", "select": "Выберите технологию", "chat": "Чатбот", "arjs": "AR.js", "playcanvas": "PlayCanvas", "babylonjs": "Babylon.js", "threejs": "Three.js", "aframevr": "A-Frame", "playcanvasDescription": "3D веб-экспортер на основе PlayCanvas", "babylonjsDescription": "3D экспортер на основе Babylon.js", "aframevrDescription": "Экспортер VR на основе A-Frame"}, "technologies": {"label": "Технология", "select": "Выберите технологию", "chat": "Чатбот", "arjs": "AR.js", "playcanvas": "PlayCanvas", "babylonjs": "Babylon.js", "threejs": "Three.js", "aframevr": "A-Frame", "chatDescription": "Интерактивный интерфейс чатбота", "arjsDescription": "Дополненная реальность с использованием AR.js и A-Frame", "playcanvasDescription": "3D веб-экспортер на основе PlayCanvas", "babylonjsDescription": "3D экспортер на основе Babylon.js", "aframevrDescription": "Экспортер VR на основе A-Frame"}, "actions": {"publish": "Опубликовать", "export": "Экспортировать", "download": "Скачать", "copy": "Копировать ссылку", "view": "Просмотр"}, "marker": {"type": "Тип маркера", "hiro": "<PERSON><PERSON> (по умолчанию)", "kanji": "Kanji", "pattern": "Шабл<PERSON>н", "barcode": "Штрих-код", "standard": "Стандартный маркер", "custom": "Свой паттерн", "presetLabel": "Предустановленный маркер", "letterA": "Буква A", "letterB": "Буква B", "letterC": "Буква C", "alt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "instruction": "Покажите этот маркер камере для активации AR"}, "success": {"published": "Успешно опубликовано!", "copied": "Ссылка скопирована в буфер обмена", "exported": "HTML файл успешно скачан"}, "errors": {"failed": "Не удалось опубликовать", "noUPDL": "В этом потоке не найдено узлов UPDL", "loadingFailed": "Не удалось загрузить AR сцену", "generationFailed": "Не удалось сгенерировать AR сцену"}, "preview": {"title": "Предпросмотр", "camera": "Разрешите доступ к камере и покажите маркер Hiro для просмотра"}, "export": {"title": "Экспорт", "description": "Экспортируйте ваш AR.js проект для локального использования", "html": "HTML код", "instructions": "Как использовать экспортированный код", "downloadInstructions": "Скачайте и разместите HTML файл на веб-сервере с HTTPS", "copyInstructions": "Скопируйте HTML код и сохраните его в файл с расширением .html"}, "arjs": {"generationMode": {"label": "Режим генерации", "streaming": "Потоковая генерация", "pregeneration": "Предварительная генерация", "streamingDescription": "Генерация AR.js в реальном времени на стороне клиента", "pregenerationDescription": "Предварительная генерация AR.js на сервере (в разработке)"}, "loading": "Загрузка AR сцены...", "publishedUrl": "Ссылка на публикацию", "copyLink": "Копировать ссылку", "viewAR": "Открыть AR просмотр", "generatingScene": "Генерация AR сцены...", "showMarker": "Покажите этот маркер камере для активации AR"}, "playcanvas": {"projectTitle": "Название проекта", "libraryVersion": {"label": "Версия PlayCanvas", "hint": "Выберите версию библиотеки"}, "loading": "Загрузка приложения PlayCanvas...", "publishedUrl": "Ссылка на публикацию", "openInBrowser": "Откройте эту ссылку в браузере для просмотра вашего PlayCanvas приложения", "viewApp": "Просмотр PlayCanvas приложения", "generationMode": {"label": "Режим генерации", "streaming": "Потоковая генерация", "pregeneration": "Предварительная генерация", "streamingDescription": "Генерация сцены в реальном времени на стороне клиента", "pregenerationDescription": "Предварительная генерация сцены на сервере (в разработке)"}, "demoMode": {"label": "Демо-режим", "off": "Выключено", "primitives": "Графические примитивы", "hint": "Выберите, как будет отображаться сцена при отсутствии логики", "offHint": "Пустая сцена с фоном", "primitivesHint": "Сцена с вращающимся красным кубом"}}, "templates": {"label": "Шаблон экспорта", "selectTemplate": "Выберите шаблон", "noTemplatesFound": "Шаблоны не найдены", "version": "v{{version}}", "description": "Описание шаблона"}, "playcanvasTemplates": {"mmoomm": {"name": "Universo MMOOMM", "description": "Многопользовательские виртуальные миры с синхронизацией и сетью в реальном времени"}}}, "arPublication": {"configSaved": "Настройки публикации AR.js сохранены", "saveError": "Ошибка при сохранении настроек", "streamingModeEnabled": "Режим потоковой генерации включен", "publicLinkAvailable": "Публичная ссылка доступна"}, "playcanvasPublication": {"configSaved": "Настройки публикации PlayCanvas сохранены", "saveError": "Ошибка при сохранении настроек PlayCanvas", "libraryHint": "Выберите версию движка PlayCanvas"}}