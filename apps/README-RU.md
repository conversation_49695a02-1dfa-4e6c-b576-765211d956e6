# Каталог приложений Universo Platformo

Здесь расположены модульные приложения, расширяющие основную платформу Flowise без изменения её ядра.

## Текущая структура

```
apps/
├── updl/                # Система узлов UPDL для создания 3D/AR/VR‑пространств
│   └── base/            # Ключевая функциональность UPDL
│       └── ...
├── profile-frt/         # Фронтенд управления профилем пользователя
│   └── base/            # Ключевая функциональность профиля
│       └── ...
├── profile-srv/         # Бекенд управления профилем (workspace-пакет)
│   └── base/            # Ключевая функциональность профиля
│       └── ...
├── publish-frt/         # Фронтенд системы публикаций для экспорта и шеринга
│   └── base/            # Ключевая функциональность фронтенда
│       ├── src/
│       │   ├── api/     # HTTP-клиенты к бекенду
│       │   ├── assets/  # Статические ресурсы (иконки, изображения)
│       │   ├── builders/# Логика обработки и конвертации UPDL
│       │   ├── ...      # Другие директории фронтенда
│       │   └── index.ts
│       └── ...
├── publish-srv/         # Бекенд системы публикаций (workspace-пакет)
│   └── base/            # Ключевая функциональность бекенда
│       ├── src/
│       │   ├── controllers/ # Контроллеры Express
│       │   ├── services/    # Бизнес-логика (напр., FlowDataService)
│       │   ├── routes/      # Асинхронные фабрики роутов
│       │   ├── types/       # Общие определения типов UPDL
│       │   └── index.ts     # Точка входа для пакета
│       └── ...
└── README.md            # Данная документация
```

## Приложения

### UPDL (Universal Platform Definition Language)

Приложение UPDL предоставляет унифицированную систему узлов для описания 3D/AR/VR-пространств, которые могут быть экспортированы на множество целевых платформ. Оно определяет стандартизированный промежуточный слой представления.

#### UPDL (updl)

**Ключевые возможности:**

-   7 основных высокоуровневых узлов для описания универсальных 3D/AR/VR-сцен
-   Устаревшие узлы (Object, Camera, Light) для обратной совместимости
-   Определения и иконки для узлов
-   Чистая интеграция с Flowise
-   Интерфейсы TypeScript

**Документация:** [apps/updl/base/README.md](./updl/base/README.md)

### Profile

Приложение Profile предоставляет функциональность управления профилями пользователей. Оно состоит из фронтенд-приложения и бекенд workspace-пакета.

#### Profile Frontend (profile-frt)

**Ключевые возможности:**

-   Аутентификация на основе JWT-токенов с Supabase
-   Функциональность обновления email и пароля
-   Адаптивный дизайн для мобильных устройств

**Документация:** [apps/profile-frt/base/README.md](./profile-frt/base/README.md)

#### Profile Server (profile-srv)

Это бекенд-сервис, структурированный как workspace-пакет (`@universo/profile-srv`), отвечающий за безопасную обработку данных профиля пользователя.

**Ключевые возможности:**

-   Безопасные эндпоинты для управления данными пользователя.
-   Использование кастомных SQL-функций с `SECURITY DEFINER` для безопасного обновления данных.
-   Асинхронная инициализация роутов для предотвращения "race conditions" с подключением к БД.

**Документация:** [apps/profile-srv/base/README.md](./profile-srv/base/README.md)

### Publish

Приложение Publish предоставляет механизмы для экспорта UPDL-пространств в AR.js и их публикации по публичным ссылкам.

#### Publish Frontend (publish-frt)

Фронтенд-приложение отвечает за весь пользовательский процесс публикации, включая финальную конвертацию данных в готовый для просмотра AR-формат.

**Ключевые возможности:**

-   **Обработка UPDL на стороне клиента**: Использует класс `UPDLProcessor` для конвертации "сырых" `flowData` с бекенда в валидный AR.js-проект. Вся тяжелая обработка происходит на клиенте.
-   **Модульные билдеры**: Гибкая система `ARJSBuilder` конструирует финальный HTML из данных UPDL.
-   **Интеграция с Supabase**: Сохраняет конфигурации публикаций.
-   **Поддержка AR-квизов**: Функциональность для создания образовательных квизов с подсчетом очков и сбором лидов.

**Документация:** [apps/publish-frt/base/README.md](./publish-frt/base/README.md)

#### Publish Backend (publish-srv)

Это бекенд-сервис, преобразованный в workspace-пакет (`@universo/publish-srv`), с единой ответственностью: предоставление данных фронтенду.

**Ключевые возможности:**

-   **Workspace-пакет**: Предоставляет общие типы и сервисы как `@universo/publish-srv`.
-   **Поставщик "сырых" данных**: Отдает `flowData` из БД, делегируя всю обработку UPDL фронтенду.
-   **Источник истины для типов**: Экспортирует все общие типы TypeScript, связанные с UPDL и публикациями.
-   **Асинхронная инициализация роутов**: Предотвращает "race conditions" путем инициализации роутов только после установки соединения с БД.

**Документация:** [apps/publish-srv/base/README.md](./publish-srv/base/README.md)

## Архитектура для будущего расширения

При расширении функциональности приложений вы можете создавать дополнительные директории, следуя этой структуре:

```
app-name/
├── base/                # Основная функциональность
│   ├── src/             # Исходный код
│   │   ├── api/         # API-клиенты (для фронтенда)
│   │   ├── assets/      # Статические ресурсы (иконки, изображения)
│   │   ├── builders/    # Билдеры из UPDL в целевые платформы (для фронтенда)
│   │   ├── components/  # React-компоненты (для фронтенда)
│   │   ├── configs/     # Конфигурационные константы
│   │   ├── controllers/ # Express-контроллеры (для бекенда)
│   │   ├── features/    # Модули функциональности (бывшие мини-приложения)
│   │   ├── hooks/       # React-хуки (для фронтенда)
│   │   ├── i18n/        # Ресурсы для интернационализации
│   │   ├── interfaces/  # TypeScript-интерфейсы и типы
│   │   ├── middlewares/ # Middleware-обработчики (для бекенда)
│   │   ├── models/      # Модели данных (для бекенда)
│   │   ├── nodes/       # Определения узлов UPDL
│   │   ├── routes/      # REST API роуты (для бекенда)
│   │   ├── services/    # Бизнес-логика (для бекенда)
│   │   ├── store/       # Управление состоянием (для фронтенда)
│   │   ├── utils/       # Утилиты
│   │   ├── validators/  # Валидация ввода (для бекенда)
│   │   └── index.ts     # Точка входа
│   ├── dist/            # Скомпилированный вывод
│   ├── package.json
│   ├── tsconfig.json
│   ├── gulpfile.ts      # (для фронтенд-модулей с ассетами)
│   └── README.md
```

**Примечание:** Не все директории обязательны для каждого приложения. Создавайте только те, которые необходимы для вашей функциональности:

-   **Фронтенд-приложениям** обычно нужны: `api/`, `assets/`, `components/`, `features/`, `pages/`, `utils/`, `gulpfile.ts`
-   **Бекенд-приложениям** обычно нужны: `controllers/`, `routes/`, `utils/`
-   **UPDL-модулям** обычно нужны: `assets/`, `interfaces/`, `nodes/`, `gulpfile.ts`

## Взаимодействия

Приложения в этой директории спроектированы для совместной работы в модульной архитектуре.

В текущей архитектуре:

```
┌──────────────┐
│              │
│   Flowise    │
│   Редактор   │
│              │
└──────┬───────┘
       │
       ▼
┌──────────────┐       ┌────────────────┐       ┌────────────────┐
│              │       │                │       │                │
│     UPDL     │──────▶│ PUBLISH-FRT    │──────▶│  PUBLISH-SRV   │
│    (Узлы)    │       │   Фронтенд     │       │     Бекенд     │
│              │       │                │       │                │
└──────────────┘       └────────────────┘       └────────┬───────┘
                                                          │
                                                          ▼
                                                 ┌────────────────┐
                                                 │                │
                                                 │ Публичный URL  │
                                                 │  /p/{uuid}     │
                                                 │                │
                                                 └────────────────┘
```

## Руководство по разработке

1.  **Модульность:** Каждое приложение должно быть самодостаточным с четкими интерфейсами.
2.  **Минимальные изменения ядра:** Избегайте модификации основной кодовой базы Flowise.
3.  **Документация:** Поддерживайте файлы README в директории каждого приложения.
4.  **Общие типы:** Используйте общие определения типов для межпрограммного взаимодействия.

## Сборка

Из корня проекта:

```bash
# Установка всех зависимостей для workspace
pnpm install

# Сборка всех приложений (и других пакетов в workspace)
pnpm build

# Сборка конкретного фронтенд-приложения
pnpm build --filter publish-frt
pnpm build --filter profile-frt
pnpm build --filter updl

# Сборка конкретного бекенд-приложения
pnpm build --filter publish-srv
pnpm build --filter profile-srv
```

## Разработка

Чтобы запустить конкретное приложение в режиме разработки (отслеживает изменения и пересобирает):

```bash
pnpm --filter publish-frt dev
pnpm --filter profile-frt dev
pnpm --filter publish-srv dev
pnpm --filter profile-srv dev
pnpm --filter updl dev
```

**Примечание о ресурсах:** Скрипты `dev` отслеживают изменения файлов TypeScript, но не копируют SVG-иконки автоматически. Если вы добавляете или изменяете SVG-ресурсы во время разработки, запустите `pnpm build --filter <app>`, чтобы они были правильно скопированы в директорию `dist`.

---

_Universo Platformo | Документация приложений_
