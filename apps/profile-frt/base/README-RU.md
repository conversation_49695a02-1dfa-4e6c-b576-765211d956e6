# Profile Frontend (profile-frt)

Фронтенд-модуль для управления профилем пользователя и аутентификации в Universo Platformo.

## Структура проекта

Проект следует единой структуре приложений в монорепозитории:

```
apps/profile-frt/base/
├── package.json
├── tsconfig.json
├── gulpfile.ts
└── src/
   ├── i18n/                # Локализация
   │  └── locales/          # Языковые файлы (en, ru)
   ├── pages/               # Компоненты страниц
   │  └── Profile.jsx       # Основная страница управления профилем
   └── index.ts             # Точка входа
```

## Возможности

-   **Обновление email**: Изменение адреса электронной почты через систему аутентификации Supabase
-   **Безопасное обновление пароля**: Изменение пароля с проверкой текущего пароля
-   **Интеграция аутентификации**: Аутентификация на основе JWT-токенов с Supabase
-   **Валидация форм**: Клиентская валидация форматов email и пароля с подтверждением пароля
-   **Обработка ошибок**: Подробные сообщения об ошибках и обратная связь с пользователем с полной интернационализацией
-   **Интернационализация**: Поддержка нескольких языков (английский/русский) включая перевод серверных ошибок
-   **Функции безопасности**: Проверка текущего пароля, bcrypt хеширование, минимальные требования к паролю
-   **Адаптивный дизайн**: Мобильно-дружественный пользовательский интерфейс

## Архитектура аутентификации

### Поток аутентификации на фронтенде

Компонент Profile обрабатывает аутентификацию пользователя и обновления профиля через безопасную систему на основе токенов:

1. **Хранение токенов**: JWT-токены хранятся в localStorage
2. **API-коммуникация**: Все запросы включают заголовок Authorization с Bearer токеном
3. **Обработка ошибок**: Корректная обработка ошибок аутентификации и валидации
4. **Обратная связь с пользователем**: Обновления статуса в реальном времени во время операций с профилем

### Интеграция с бекендом

Фронтенд взаимодействует с бекендом через REST API эндпоинты:

#### Эндпоинт обновления Email

```
PUT /api/v1/auth/email
Headers: Authorization: Bearer <jwt_token>
Body: { "email": "<EMAIL>" }
```

#### Эндпоинт обновления пароля

```
PUT /api/v1/auth/password
Headers: Authorization: Bearer <jwt_token>
Body: {
    "currentPassword": "oldpassword123",
    "newPassword": "newpassword123"
}
```

### Интеграция с Supabase

Система управления профилем использует аутентификацию Supabase с пользовательскими SQL-функциями для безопасного обновления данных пользователя.

#### SQL-функции в базе данных

Система аутентификации использует SQL-функции с привилегиями `SECURITY DEFINER` для обновления данных пользователя:

**Функция обновления Email:**

```sql
CREATE OR REPLACE FUNCTION update_user_email(user_id uuid, new_email text)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    UPDATE auth.users SET email = new_email WHERE id = user_id;
END;
$$;
```

**Функция проверки пароля:**

```sql
CREATE OR REPLACE FUNCTION verify_user_password(password text)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_id uuid;
BEGIN
    user_id := auth.uid();

    RETURN EXISTS (
        SELECT id
        FROM auth.users
        WHERE id = user_id
        AND encrypted_password = crypt(password::text, auth.users.encrypted_password)
    );
END;
$$;
```

**Функция безопасного обновления пароля:**

```sql
CREATE OR REPLACE FUNCTION change_user_password_secure(
    current_password text,
    new_password text
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_id uuid;
    is_valid_password boolean;
BEGIN
    -- Получение текущего ID пользователя
    user_id := auth.uid();

    -- Проверка текущего пароля
    SELECT verify_user_password(current_password) INTO is_valid_password;

    IF NOT is_valid_password THEN
        RAISE EXCEPTION 'Current password is incorrect';
    END IF;

    -- Обновление пароля
    UPDATE auth.users
    SET encrypted_password = crypt(new_password, gen_salt('bf'))
    WHERE id = user_id;

    RETURN json_build_object('success', true, 'message', 'Password updated successfully');
END;
$$;
```

#### Реализация на бекенде

Контроллеры бекенда используют эти SQL-функции через RPC-вызовы Supabase:

```typescript
// Обновление email
const { error } = await supabase.rpc('update_user_email', {
    user_id: userId,
    new_email: email
})

// Безопасное обновление пароля с проверкой текущего пароля
const { data, error } = await supabase.rpc('change_user_password_secure', {
    current_password: currentPassword,
    new_password: newPassword
})
```

## Миграция базы данных

Система управления профилями теперь имеет собственную выделенную структуру миграций, поддерживая правильное разделение ответственности между функциональностью профиля и другими компонентами системы.

### Миграция сервиса профилей

Система профилей использует выделенную миграцию `apps/profile-srv/base/src/database/migrations/postgres/1741277504477-AddProfile.ts`, которая включает:

-   **Схема таблицы профилей**: Полная структура данных профиля пользователя
-   **Политики Row Level Security (RLS)**: Безопасный контроль доступа к данным профиля
-   **SQL-функции профиля пользователя**: Функции аутентификации и управления профилем
-   **Триггеры базы данных**: Автоматическое создание профиля при регистрации пользователя

### Интеграция с основной платформой

Сервис профилей интегрируется с основной платформой Flowise через:

-   **Интеграция сущностей**: Сущности профиля автоматически включаются в основную схему базы данных
-   **Система миграций**: Миграции профилей интегрированы в систему миграций PostgreSQL
-   **Общая аутентификация**: Использует ту же систему аутентификации Supabase, что и основная платформа

## Пользовательский интерфейс

### Форма управления профилем

Компонент Profile предоставляет интуитивный интерфейс для управления учетной записью пользователя:

-   **Секция Email**: Отображение текущего email с формой обновления
-   **Секция пароля**: Безопасная форма изменения пароля
-   **Валидация**: Валидация форм в реальном времени с сообщениями об ошибках
-   **Обратная связь**: Уведомления об успехе/ошибках для всех операций
-   **Состояния загрузки**: Визуальные индикаторы во время API операций

### Валидация форм

Клиентская валидация включает:

-   **Формат Email**: Проверка корректного формата адреса электронной почты
-   **Требования к паролю**: Валидация минимальной длины (6+ символов) и сложности
-   **Проверка текущего пароля**: Обязательная проверка текущего пароля перед изменениями
-   **Подтверждение пароля**: Подтверждение нового пароля для предотвращения опечаток
-   **Обязательные поля**: Предотвращение отправки пустых форм
-   **Отображение ошибок**: Четкие сообщения об ошибках для некорректного ввода с полной интернационализацией

## Примеры использования

### Базовый поток обновления профиля

Вот типичный поток взаимодействия пользователя для обновления информации профиля:

```javascript
// 1. Пользователь открывает страницу профиля
// 2. Текущий email отображается автоматически
// 3. Пользователь вводит новый email и отправляет форму

const updateEmail = async (newEmail) => {
    const token = localStorage.getItem('token')
    const response = await fetch('/api/v1/auth/email', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`
        },
        body: JSON.stringify({ email: newEmail })
    })

    if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to update email')
    }

    // Уведомление об успехе для пользователя
    return await response.json()
}
```

### Пример обработки ошибок

```javascript
try {
    await updateEmail('<EMAIL>')
    setSuccess('Email успешно обновлен!')
} catch (error) {
    setError(`Не удалось обновить email: ${error.message}`)
}
```

## Архитектура API

### Заголовки аутентификации

Все API-запросы включают правильную аутентификацию:

```javascript
const token = localStorage.getItem('token')
const headers = {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${token}`
}
```

### Обработка ошибок

Комплексная обработка ошибок для различных сценариев:

-   **401 Unauthorized**: Недействительные или истекшие токены
-   **400 Bad Request**: Ошибки валидации или отсутствующие данные
-   **500 Server Error**: Ошибки базы данных или системы
-   **Network Errors**: Проблемы с соединением и таймауты

### Обработка ответов

Все API-ответы обрабатываются с правильной проверкой ошибок:

```javascript
const response = await fetch(url, { method, headers, body })
const data = await response.json()

if (!response.ok) {
    throw new Error(data.error || 'Operation failed')
}
```

## Функции безопасности

### Аутентификация на основе токенов

-   **JWT-токены**: Безопасная аутентификация на основе токенов
-   **Валидация токенов**: Проверка токенов на стороне сервера
-   **Автоматическое истечение**: Обработка истечения токенов
-   **Безопасное хранение**: localStorage с правильной очисткой
-   **Проверка текущего пароля**: Обязательная проверка перед изменением пароля
-   **Перевод серверных ошибок**: Автоматическое сопоставление серверных ошибок с локализованными сообщениями

### Безопасность SQL-функций

-   **SECURITY DEFINER**: Повышенные привилегии для операций с базой данных
-   **Валидация ввода**: Защита от SQL-инъекций
-   **Изоляция пользователей**: Операции ограничены данными аутентифицированного пользователя
-   **Аудиторский след**: Все операции логируются для мониторинга безопасности

### Безопасность паролей

-   **Bcrypt хеширование**: Промышленный стандарт хеширования паролей
-   **Генерация соли**: Уникальные соли для каждого пароля
-   **Безопасная передача**: Передача паролей только по HTTPS
-   **Без открытого текста**: Пароли никогда не хранятся в открытом виде

## Разработка

### Настройка

```bash
pnpm install
```

### Режим разработки

```bash
pnpm --filter profile-frt dev
```

### Сборка

```bash
pnpm --filter profile-frt build
```

### Процесс сборки

1. **Компиляция TypeScript**: Компиляция файлов TypeScript в JavaScript
2. **Задачи Gulp**: Копирование статических ресурсов (JSON, CSS и т.д.) в папку dist

## Зависимости

Приложение использует минимальные зависимости для оптимальной производительности:

-   **React 18**: Современный React для разработки компонентов
-   **TypeScript**: Типобезопасность и улучшенный опыт разработки
-   **Инструменты сборки**: Gulp для управления ресурсами, rimraf для очистки

## Интеграция с основной платформой

Фронтенд профиля беспрепятственно интегрируется с основной платформой Flowise:

-   **Последовательная аутентификация**: Использует ту же систему JWT-токенов, что и основная платформа
-   **Общая база API**: Использует существующую инфраструктуру бекенда
-   **Модульная архитектура**: Независимое развертывание и разработка
-   **Согласованность темы**: Следует руководящим принципам дизайна основной платформы

## Текущие ограничения

-   **Контекст одного пользователя**: В настоящее время предназначен для управления индивидуальным пользователем
-   **Базовая валидация**: Ограниченные правила валидации на стороне клиента
-   **Нет поддержки аватаров**: Фотографии профиля пока не реализованы

## Будущие улучшения

-   **Фотографии профиля**: Загрузка и управление аватарами
-   **Расширенная валидация**: Продвинутые требования к сложности пароля
-   **Пользовательские предпочтения**: Дополнительные настройки профиля и предпочтения
-   **Социальный вход**: Интеграция с провайдерами социальной аутентификации
-   **Двухфакторная аутентификация**: Улучшенные параметры безопасности
-   **История профиля**: Журнал аудита для изменений профиля

## Обновления (Июнь 2025)

### Улучшения архитектуры профиля

1. **Отображение никнейма и Email** — Фронтенд теперь корректно получает и отображает никнейм пользователя из таблицы `profiles` вместе с email.
2. **Безопасная смена пароля** — Запросы на смену пароля отправляются на новый эндпоинт (`PUT /api/v1/auth/password`), который проверяет текущий пароль через безопасную SQL-функцию Supabase.
3. **Обработка токена** — Все операции профиля формируют заголовок Authorization с JWT-токеном, полученным из `AuthProvider.getAccessToken()`.
4. **Обратная связь об ошибках** — Компонент выводит точные сообщения об ошибках (например, _Текущий пароль неверен_), полученные от бекенда.

### Зачем добавлены индексы в `Profile.ts`

В сущности `Profile` на бекенде добавлены два явных индекса:

| Индекс                  | Колонка    | Назначение                                                                                                               |
| ----------------------- | ---------- | ------------------------------------------------------------------------------------------------------------------------ |
| `idx_profiles_user_id`  | `user_id`  | Гарантирует связь «один к одному» между `auth.users` и `public.profiles`, ускоряет поиск по идентификатору пользователя. |
| `idx_profiles_nickname` | `nickname` | Обеспечивает уникальность никнейма и ускоряет проверку его доступности при выборе/изменении.                             |

Эти индексы существенно уменьшают задержку при загрузке профиля и проверке никнейма.

---

_Universo Platformo | Модуль фронтенда профиля_
