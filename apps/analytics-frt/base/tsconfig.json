{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020", "DOM"], "declaration": true, "sourceMap": true, "outDir": "./dist", "strict": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "skipLibCheck": true, "jsx": "react", "allowJs": true, "baseUrl": ".", "paths": {"*": ["*"]}, "moduleResolution": "node"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx"]}