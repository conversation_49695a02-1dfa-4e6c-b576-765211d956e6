# Authentication Frontend (auth-frt)

Фронтенд система аутентификации для Universo Platformo на основе интеграции с Supabase.

## Обзор проекта

Этот модуль предоставляет полную систему аутентификации, которая заменяет устаревшую аутентификацию Flowise современной системой на основе Supabase. Система обеспечивает безопасную аутентификацию пользователей и предоставляет последовательную обработку ошибок во всех компонентах приложения.

## Архитектура аутентификации

### Миграция с устаревшей системы

Система аутентификации была перенесена с устаревшей системы Flowise (username/password в localStorage) на современную систему на основе Supabase с JWT токенами и правильным потоком аутентификации.

#### Устаревшая система (Удалена)

-   **Компонент LoginDialog**: Модальная аутентификация с базовым username/password
-   **Хранение localStorage**: Учетные данные хранились в localStorage браузера
-   **Ручная обработка ошибок**: Каждый компонент обрабатывал 401 ошибки индивидуально

#### Новая система (Текущая)

-   **Компонент страницы Auth**: Полностраничный интерфейс аутентификации
-   **AuthProvider Context**: Централизованное управление состоянием аутентификации
-   **Хранение JWT токенов**: Безопасная аутентификация на основе токенов
-   **Единая обработка ошибок**: Последовательная обработка ошибок аутентификации через пользовательский хук

### Компоненты системы

```
packages/ui/src/
├── views/up-auth/
│   └── Auth.jsx                    # Основная страница аутентификации
├── utils/
│   └── authProvider.jsx            # Провайдер контекста аутентификации
├── routes/
│   ├── AuthGuard.jsx              # Компонент защиты маршрутов
│   └── index.jsx                  # Основная конфигурация маршрутизации
└── hooks/
    └── useAuthError.js            # Обработчик ошибок аутентификации
```

## Основные компоненты

### 1. Страница аутентификации (`Auth.jsx`)

Основной интерфейс аутентификации, который обрабатывает вход и регистрацию пользователей.

**Возможности:**

-   Аутентификация по email/паролю
-   Регистрация пользователей
-   Функция сброса пароля
-   Интеграция с Supabase
-   Перевод сообщений об ошибках
-   Адаптивный дизайн

**Ключевые функции:**

```javascript
// Функция входа
const handleLogin = async (email, password) => {
    const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
    })
}

// Функция регистрации
const handleRegister = async (email, password) => {
    const { data, error } = await supabase.auth.signUp({
        email,
        password
    })
}
```

### 2. Провайдер аутентификации (`authProvider.jsx`)

Централизованное управление состоянием аутентификации с использованием React Context.

**Предоставляет:**

-   Состояние аутентификации пользователя
-   Функции входа/выхода
-   Управление токенами
-   Информация профиля пользователя

**Структура контекста:**

```javascript
const AuthContext = createContext({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    login: () => {},
    logout: () => {},
    register: () => {}
})
```

### 3. Охранник аутентификации (`AuthGuard.jsx`)

Компонент защиты маршрутов, который обеспечивает доступ только аутентифицированным пользователям к защищенным маршрутам.

**Возможности:**

-   Контроль доступа на основе маршрутов
-   Автоматическое перенаправление на страницу аутентификации
-   Управление состоянием загрузки
-   Сохранение пути возврата

### 4. Хук ошибок аутентификации (`useAuthError.js`)

Пользовательский хук для последовательной обработки ошибок аутентификации в компонентах.

**Назначение:**

-   Централизует обработку 401 ошибок
-   Обеспечивает автоматический выход при ошибке аутентификации
-   Перенаправляет на страницу аутентификации с путем возврата
-   Устраняет дублирование кода

**Пример использования:**

```javascript
import { useAuthError } from '@/hooks/useAuthError'

const MyComponent = () => {
    const { handleAuthError } = useAuthError()

    useEffect(() => {
        if (apiError) {
            if (!handleAuthError(apiError)) {
                // Обработка не-аутентификационных ошибок
                setError(apiError)
            }
        }
    }, [apiError, handleAuthError])
}
```

## Реализация миграции

### Перенесенные компоненты

Следующие компоненты были успешно перенесены с устаревшей аутентификации:

1. **`packages/ui/src/views/chatflows/index.jsx`**

    - Удалено: Импорт и использование `LoginDialog`
    - Добавлено: Интеграция хука `useAuthError`
    - Удалено: Ручная обработка 401 ошибок

2. **`packages/ui/src/views/agentflows/index.jsx`**

    - Удалено: Импорт и использование `LoginDialog`
    - Добавлено: Интеграция хука `useAuthError`
    - Удалено: Ручная обработка 401 ошибок

3. **`packages/ui/src/views/up-uniks/UnikList.jsx`**

    - Удалено: Импорт и использование `LoginDialog`
    - Добавлено: Интеграция хука `useAuthError`
    - Удалено: Ручная обработка 401 ошибок

4. **`packages/ui/src/views/publish/bots/BaseBot.jsx`**

    - Удалено: Импорт и использование `LoginDialog`
    - Добавлено: Интеграция хука `useAuthError`
    - Улучшено: Логика доступа к публичному/приватному API

5. **`packages/ui/src/layout/MainLayout/Header/ProfileSection/index.jsx`**
    - Обновлено: Проверка статуса аутентификации с использованием `useAuth`
    - Заменено: Проверка localStorage username/password на `isAuthenticated`

### Удаленные компоненты

-   **`packages/ui/src/ui-component/dialog/LoginDialog.jsx`** - Полностью удален как больше не нужный

### Миграция обработки ошибок

**До (Устаревшая система):**

```javascript
useEffect(() => {
    if (apiError?.response?.status === 401) {
        setLoginDialogProps({
            title: 'Login',
            confirmButtonName: 'Login'
        })
        setLoginDialogOpen(true)
    } else {
        setError(apiError)
    }
}, [apiError])
```

**После (Новая система):**

```javascript
useEffect(() => {
    if (apiError) {
        if (!handleAuthError(apiError)) {
            setError(apiError)
        }
    }
}, [apiError, handleAuthError])
```

## Поток аутентификации

### 1. Начальная загрузка

1. Приложение загружается и проверяет состояние аутентификации
2. `AuthProvider` проверяет существующую сессию с Supabase
3. Маршруты, защищенные `AuthGuard`, проверяют статус аутентификации

### 2. Неаутентифицированный доступ

1. Пользователь пытается получить доступ к защищенному маршруту
2. `AuthGuard` обнаруживает неаутентифицированное состояние
3. Пользователь перенаправляется на страницу `/auth` с путем возврата

### 3. Процесс аутентификации

1. Пользователь вводит учетные данные на странице аутентификации
2. Supabase проверяет учетные данные
3. JWT токен сохраняется в сессии
4. Пользователь перенаправляется к исходному назначению

### 4. Обработка ошибок API

1. API запрос возвращает статус 401
2. Хук `useAuthError` обнаруживает ошибку аутентификации
3. Пользователь автоматически выходит из системы
4. Перенаправление на страницу аутентификации с текущим путем

## Функции безопасности

### Управление токенами

-   JWT токены безопасно хранятся в сессии Supabase
-   Автоматическое обновление токенов
-   Безопасный процесс выхода

### Защита маршрутов

-   Все чувствительные маршруты защищены `AuthGuard`
-   Автоматическое перенаправление для неаутентифицированных пользователей
-   Сохранение пути возврата

### Обработка ошибок

-   Централизованная обработка ошибок аутентификации
-   Автоматическая очистка при ошибке аутентификации
-   Понятные для пользователя сообщения об ошибках

## Интеграция с Supabase

### Конфигурация

```javascript
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY

export const supabase = createClient(supabaseUrl, supabaseAnonKey)
```

### Методы аутентификации

-   Аутентификация по email/паролю
-   Регистрация пользователей с подтверждением email
-   Функция сброса пароля
-   Управление сессиями

## Будущие улучшения

### Запланированные функции

1. **Интеграция OAuth**: Google, GitHub и другие провайдеры
2. **Многофакторная аутентификация**: Поддержка SMS и приложений аутентификации
3. **Управление сессиями**: Расширенные элементы управления сессиями
4. **Журналирование аудита**: Отслеживание событий аутентификации

### Дорожная карта миграции

1. **Фаза 1**: Полная миграция в структуру `apps/auth-frt`
2. **Фаза 2**: Расширенные функции безопасности
3. **Фаза 3**: Продвинутые методы аутентификации
4. **Фаза 4**: Полные функции аудита и соответствия

## Руководящие принципы разработки

### Добавление новых защищенных компонентов

1. Импортируйте и используйте хук `useAuthError` для обработки ошибок API
2. Оберните маршруты компонентом `AuthGuard`
3. Используйте хук `useAuth` для состояния аутентификации

### Лучшие практики обработки ошибок

```javascript
// Всегда используйте useAuthError для ошибок API
const { handleAuthError } = useAuthError()

useEffect(() => {
    if (error) {
        // Пусть useAuthError обрабатывает 401, остальные обрабатывайте вручную
        if (!handleAuthError(error)) {
            setLocalError(error)
        }
    }
}, [error, handleAuthError])
```

### Доступ к состоянию аутентификации

```javascript
// Используйте контекст AuthProvider для состояния аутентификации
const { user, isAuthenticated, login, logout } = useAuth()
```

## Тестирование

### Тестирование потока аутентификации

1. Тестирование доступа к неаутентифицированному маршруту
2. Проверка функциональности страницы аутентификации
3. Тестирование автоматического выхода при 401 ошибках
4. Проверка функциональности пути возврата

### Тестирование обработки ошибок

1. Тестирование обработки 401 ошибок API
2. Проверка автоматического перенаправления
3. Тестирование обработки истечения сессии

## Устранение неполадок

### Распространенные проблемы

**1. Аутентификация не работает**

-   Проверьте конфигурацию Supabase
-   Проверьте переменные окружения
-   Проверьте сетевое подключение

**2. 401 ошибки не обрабатываются**

-   Убедитесь, что хук `useAuthError` импортирован и используется
-   Проверьте структуру объекта ошибки

**3. Циклы перенаправления**

-   Проверьте реализацию `AuthGuard`
-   Проверьте конфигурацию маршрутов

### Режим отладки

Включите журналирование отладки, установив:

```javascript
localStorage.setItem('auth_debug', 'true')
```
